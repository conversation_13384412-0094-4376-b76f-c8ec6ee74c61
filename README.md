# 新爬虫项目
##  项目目录结构介绍
- common 通用类库
  - const 通用常量类库
  - enums 通用枚举类库
  - utils 通用工具类库
-  config 通用配置
  - mq mq统一配置
  - thread 线程统一配置
- dispatch 调度中心服务
  - crawler 爬虫调度中心服务
  - hangsi  航司调度中心服务
- module 业务服务模块
  - crawler 爬虫业务服务
    - controller 控制层
    - domain  业务实体存放
    - mapper  dao层
    - service 服务层
    - util   业务对应工具类
  - hangsi 航司业务服务
- mq 消息服务模块
  - bean 消息服务基类（父类）定义模块
  - consumer 消费者服务
  - producer 生产者服务
  - rep 响应结果对象定义模块
  - req 请求参数对象定义模块
##  项目环境配置
-  application.yml 基础配置信息配置类
- application-dev.yml 本地环境配置信息配置类
- application-test.yml 测试环境配置信息配置类
- application-uat.yml 预发布生产（业务验证）环境配置信息配置类
- application-prod.yml 生产环境配置信息配置类