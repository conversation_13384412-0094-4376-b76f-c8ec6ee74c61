spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: **********************************************************************************************************************************************************************************************************
    username: root
    password: LLq@2025!0924
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 500
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 配置扩展属性，用于监控统计分析SQL性能等
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

  # Mq
  rabbitmq:
    host: *************
    port: 5672
    username: admin
    password: Llq202507#
    #默认虚拟主机
    virtual-host: /dev
    #连接超时时间10秒，超过则连接失败
    connection-timeout: 5000ms
    #关联的生产者确认机制 - 确保消息成功到达RabbitMQ Broker
    publisher-confirm-type: correlated
    #生产者返回机制 - 当消息无法路由到任何队列时，返回给生产者
    publisher-returns: true
    #生产者返回机制(publisher-returns)的触发开关
    template:
      mandatory: true
      #消费者并发配置
    listener:
      simple:
        #初始消费者数量
        concurrency: 20
        #最大消费者并发处理数量
        max-concurrency: 40
        #手动确认模式
        acknowledge-mode: manual
        #预取计数：每个消费者最多同时处理10条未确认的消息
        prefetch: 10
        #重试策略
        retry:
          # 启用重试机制
          enabled: true
          # 最大重试次数（包括第一次尝试）
          max-attempts: 3
          #第一次重试的间隔：2秒
          initial-interval: 2000ms
          # 间隔倍数：每次重试间隔乘以2
          multiplier: 2
          # 最大重试间隔：10秒
          max-interval: 10000ms
# threadPool Config
thread:
    task:
      core: 2
      max: 4
      queue: 10
#  mail:
#    path: C:/lcc/mail
#    server: http://127.0.0.1:8787/vj
rabbitConfig:
  exchangeName: direct.exchange
  consumer:
    queueName: s7Queue
  producer:
      queueName: s7Queue
      routingKey: s7Queue
  producerRep:
    queueName: crawlerRepQueen
    routingKey: crawlerRepQueen