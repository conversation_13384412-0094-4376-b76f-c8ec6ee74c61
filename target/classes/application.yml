server:
  port: 8788
  servlet:
    context-path: /crawler
  tomcat:
    uri-encoding: UTF-8

spring:
  profiles:
    active: dev
  application:
    name: crawler
  banner:
    location: banner.txt

mybatis-plus:
  #mapper配置文件。可能有多个，所以Mapper.xml结尾的都添加进来
  mapper-locations: classpath*:/mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: off
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      update-strategy: not_null  #更新策略，只更新非空字段




