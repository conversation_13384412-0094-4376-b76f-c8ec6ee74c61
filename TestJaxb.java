import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.StringWriter;

@XmlRootElement
class TestObject {
    private String name = "test";
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
}

public class TestJaxb {
    public static void main(String[] args) {
        try {
            TestObject obj = new TestObject();
            JAXBContext context = JAXBContext.newInstance(TestObject.class);
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            StringWriter writer = new StringWriter();
            marshaller.marshal(obj, writer);
            System.out.println("JAXB 工作正常！");
            System.out.println(writer.toString());
        } catch (Exception e) {
            System.out.println("JAXB 错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
