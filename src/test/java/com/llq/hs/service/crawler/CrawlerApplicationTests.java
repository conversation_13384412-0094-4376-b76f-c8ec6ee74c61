package com.llq.hs.service.crawler;

import com.llq.hs.service.crawler.module.crawler.service.VjPriceBaseCrawlerServiceImpl;
import com.llq.hs.service.crawler.mq.producer.MessageService;
import com.llq.hs.service.crawler.mq.req.SaasSysRequestMessage;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class CrawlerApplicationTests {
    @Autowired
    private MessageService messageService;

    @Test
    void contextLoads() {
        for (int i = 0; i < 1000; i++) {
            SaasSysRequestMessage saasSysRequestMessageVj = new SaasSysRequestMessage();
            saasSysRequestMessageVj.setHangSi("VJ");
            saasSysRequestMessageVj.setDepCity("出发城市三字码");
            saasSysRequestMessageVj.setArrCity("到达城市三字码");
            saasSysRequestMessageVj.setDepartureDate("出发日期");
            saasSysRequestMessageVj.setCurrency("货币类型");
            saasSysRequestMessageVj.setDepAirport("出发机场三字码");
            saasSysRequestMessageVj.setArrAirport("到达机场三字码");
            messageService.sendMessage(saasSysRequestMessageVj);
        }

//        SaasSysRequestMessage saasSysRequestMessageS7 = new SaasSysRequestMessage();
//        saasSysRequestMessageS7.setHangSi("S7");
//        saasSysRequestMessageS7.setDepCity("出发城市三字码");
//        saasSysRequestMessageS7.setArrCity("到达城市三字码");
//        saasSysRequestMessageS7.setDepartureDate("出发日期");
//        saasSysRequestMessageS7.setCurrency("货币类型");
//        saasSysRequestMessageS7.setDepAirport("出发机场三字码");
//        saasSysRequestMessageS7.setArrAirport("到达机场三字码");
    }

}
