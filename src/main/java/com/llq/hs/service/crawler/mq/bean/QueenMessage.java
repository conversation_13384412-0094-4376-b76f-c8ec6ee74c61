package com.llq.hs.service.crawler.mq.bean;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * @description: 队列消息入参实体类
 * <AUTHOR>
 * @date 2025/11/3 11:39
 */
@Data
public class QueenMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Integer id;
    private String message;
    private LocalDateTime createTime;

}
