package com.llq.hs.service.crawler.VJ.enums;

import com.llq.hs.service.crawler.VJ.config.MailConfig;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 2025/07/14
 **/
@Getter
public enum MailGroupConfig {

    //有效期180天 开始日期2025-07-14 截止到期日 2026-01-10
    VJ(AirLineType.VJ, MailConfig.userName,MailConfig.authCode, MailHostType.TY126),
    ;

    private AirLineType airLine;
    private String username;
    private String authCode;
    private MailHostType hostType;


    MailGroupConfig(AirLineType airLine, String username, String authCode, MailHostType hostType) {
        this.airLine = airLine;
        this.username = username;
        this.authCode = authCode;
        this.hostType = hostType;
    }

    /**
     * 获取航司邮箱配置
     * @param airLine
     * @return
     */
    public static MailGroupConfig getConfigByAir(AirLineType airLine){
        for (MailGroupConfig groupType : MailGroupConfig.values()) {
            if (groupType.getAirLine().equals(airLine)){
                return groupType;
            }
        }
        throw new RuntimeException("此航司邮箱未配置：" + airLine.getCode());
    }



}
