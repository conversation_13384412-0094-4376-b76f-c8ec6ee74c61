package com.llq.hs.service.crawler.dispatch.hangsi;



import cn.hutool.core.util.StrUtil;
import com.llq.hs.service.crawler.dispatch.hangsi.service.BaseAirlineService;
import com.llq.hs.service.crawler.mq.consumer.RabbitAbstractMesageDispose;
import com.llq.hs.service.crawler.mq.req.SaasSysRequestMessage;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @description: 航司调度中心，由调度中心对不同航司进行服务调用
 * 新增航司只需要新增对应的impl实现类并实现BaseAirlineService接口即可
 * 增加之后调度中心会自动将其注册（注入）
 * @date 2025/11/3 16:45
 */
@Component
public class HangSiDispatchCenter extends RabbitAbstractMesageDispose {
    private static final Logger logger = LoggerFactory.getLogger(HangSiDispatchCenter.class);

    private final Map<String, BaseAirlineService> airlineServiceMap = new ConcurrentHashMap<>();

    /**
     * 通过构造函数自动注入所有航司服务
     */
    @Autowired
    public HangSiDispatchCenter(List<BaseAirlineService> airlineServices) {
        for (BaseAirlineService service : airlineServices) {
            airlineServiceMap.put(service.getAirlineCode().toUpperCase(), service);
            logger.info("注册航司服务: {}", service.getAirlineCode());
        }
        logger.info("航司服务注册完成，共注册 {} 个服务", airlineServiceMap.size());
    }

    /**
     * 调度方法
     */
    public void dispatch(SaasSysRequestMessage message, Channel channel, Message amqpMessage) {
        if (message == null || StrUtil.isBlank(message.getHangSi())) {
            logger.warn("消息或航司代码为空");
            return;
        }

        String hangSi = message.getHangSi().toUpperCase();
        //获取航司服务
        BaseAirlineService airlineService = airlineServiceMap.get(hangSi);

        if (airlineService != null) {
            logger.info("开始处理 {} 航司消息", hangSi);
            airlineService.processMessage(message, channel, amqpMessage,hangSi);
        } else {
            logger.warn("未找到对应的航司服务: {}", hangSi);
            // 拒绝该条消息并将其丢弃---只消费在调度中心注册了的航司消息
            basicNack(channel, amqpMessage,hangSi);
        }
    }

    /**
     * 获取已注册的航司服务数量（用于监控）
     */
    public int getRegisteredServiceCount() {
        return airlineServiceMap.size();
    }

    /**
     * 检查是否支持某个航司
     */
    public boolean supportsAirline(String airlineCode) {
        return airlineServiceMap.containsKey(airlineCode.toUpperCase());
    }

}
