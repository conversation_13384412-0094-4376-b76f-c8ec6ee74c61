package com.llq.hs.service.crawler.module.crawler.util.http;

import com.llq.hs.service.crawler.mq.consumer.MqMessageConfirm;
import com.llq.hs.service.crawler.mq.consumer.RabbitAbstractMesageDispose;
import com.llq.hs.service.crawler.mq.producer.MessageService;
import com.llq.hs.service.crawler.mq.rep.Lcc_ZhengCe;
import com.llq.hs.service.crawler.mq.req.SaasSysRequestMessage;
import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.net.URI;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @description: 异步HTTP请求+消息队列工具类
 * 场景：爬虫
 * 场景特点：响应结果较长，需要即时请求响应以提升消息消费速度，防止消息堆积队列，爬虫结果异步等待并存入消息队列
 * @date 2025/11/5
 */
@Component
public class AsyncHttpClientWithMQ extends RabbitAbstractMesageDispose {

    private static final Logger logger = LoggerFactory.getLogger(AsyncHttpClientWithMQ.class);

    private final WebClient webClient;
    private final MessageService messageService;
    private final AtomicLong requestCounter = new AtomicLong(0);
    private final Map<String, RequestStats> requestStats = new ConcurrentHashMap<>();
    //默认超时-秒
    private static final int TIMEOUT=30;
    // 信号量控制
    private final Semaphore requestSemaphore;
    // 异步任务数量--信号量
    private static final int MAX_CONCURRENT_REQUESTS = 1800;
    // 获取信号量超时时间
    private static final int SEMAPHORE_TIMEOUT_SECONDS = 10;

    // 连接池配置
    public AsyncHttpClientWithMQ(MessageService messageService) {
        this.messageService = messageService;
        // 初始化信号量
        this.requestSemaphore = new Semaphore(MAX_CONCURRENT_REQUESTS, true);
        // 配置连接池
        ConnectionProvider connectionProvider = ConnectionProvider.builder("crawler-pool")
                // 连接池中保持的最大活跃连接数/个
                .maxConnections(200)
                //连接最大空闲时间，超过此时间未使用的连接将被释放/秒
                .maxIdleTime(Duration.ofSeconds(30))
                // 连接最大生命周期，无论是否活跃，超过此时间的连接都将被关闭重建/分钟
                .maxLifeTime(Duration.ofMinutes(5))
                // 从连接池获取连接的最大等待时间，超时则抛出异常/秒
                .pendingAcquireTimeout(Duration.ofSeconds(10))
                // 等待队列
                //.pendingAcquireMaxCount(1000)
                // 后台连接清理任务的执行间隔，定期检查和清理过期连接/秒
                .evictInBackground(Duration.ofSeconds(120))
                // FIFO策略，更公平
                //.fifo()
                .build();

        HttpClient httpClient = HttpClient.create(connectionProvider)
                .responseTimeout(Duration.ofSeconds(30))
                .compress(true)
                .keepAlive(true);
                // 连接超时15秒
//                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 15000)
//                .option(ChannelOption.SO_KEEPALIVE, true)
//                // 禁用Nagle算法
//                .option(ChannelOption.TCP_NODELAY, true)
//                .doOnConnected(conn -> {
//                    conn.addHandlerLast(new ReadTimeoutHandler(120));
//                    conn.addHandlerLast(new WriteTimeoutHandler(120));
//                });

        this.webClient = WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.USER_AGENT, "Crawler-Async-MQ-Client/1.0")
                .build();
    }

    // 请求统计信息
    private static class RequestStats {
        long totalRequests;
        long successRequests;
        long failedRequests;
        long totalResponseTime;
        //信号量拒绝统计
        long semaphoreRejections;
    }

    // 请求配置
    public static class RequestConfig {
        private Duration timeout = Duration.ofSeconds(TIMEOUT);
        private Map<String, String> headers;
        private Map<String, String> queryParams;
        // 航司代码
        private String airline;
        // 任务类型：可能会存在的扩展
        private String taskType;

        public static RequestConfig builder() {
            return new RequestConfig();
        }

        public RequestConfig timeout(Duration timeout) {
            this.timeout = timeout;
            return this;
        }

        public RequestConfig headers(Map<String, String> headers) {
            this.headers = headers;
            return this;
        }

        public RequestConfig queryParams(Map<String, String> queryParams) {
            this.queryParams = queryParams;
            return this;
        }

        public RequestConfig airline(String airline) {
            this.airline = airline;
            return this;
        }

        public RequestConfig taskType(String taskType) {
            this.taskType = taskType;
            return this;
        }

        public RequestConfig build() {
            return this;
        }

        public Duration getTimeout() { return timeout; }
        public Map<String, String> getHeaders() { return headers; }
        public Map<String, String> getQueryParams() { return queryParams; }
        public String getAirline() { return airline; }
        public String getTaskType() { return taskType; }
    }

    // 异步请求结果
    public static class AsyncRequestResult {
        private final String requestId;
        private final boolean success;
        private final String message;

        public AsyncRequestResult(String requestId, boolean success, String message) {
            this.requestId = requestId;
            this.success = success;
            this.message = message;
        }

        public String getRequestId() { return requestId; }
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
    }


    /**
     * 执行异步爬虫请求并自动发送结果到消息队列-get
     */
    public AsyncRequestResult executeCrawlerAsync(String url, RequestConfig config, MqMessageConfirm mqMessageConfirm) {
        String requestId = generateRequestId(config.getAirline(), config.getTaskType());
        long startTime = System.currentTimeMillis();

        // 1. 尝试获取信号量许可
        boolean acquired = false;
        try {
            acquired = requestSemaphore.tryAcquire(SEMAPHORE_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            if (!acquired) {
                logger.warn("[{}] 系统繁忙，无法获取请求许可，当前并发数: {}/{}",
                        requestId, getActiveRequestCount(), MAX_CONCURRENT_REQUESTS);
                recordSemaphoreRejection();
                return new AsyncRequestResult(requestId, false, "系统繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            logger.error("[{}] 获取请求许可被中断", requestId, e);
            Thread.currentThread().interrupt();
            return new AsyncRequestResult(requestId, false, "请求被中断");
        }

        logger.info("[{}] 开始异步爬虫请求: {}, 航司: {}, 任务类型: {}, 当前并发: {}/{}",
                requestId, url, config.getAirline(), config.getTaskType(),
                getActiveRequestCount(), MAX_CONCURRENT_REQUESTS);

        URI uri = buildUri(url, config);

        // 执行异步HTTP请求
        webClient.get()
                .uri(uri)
                .headers(headers -> addCustomHeaders(headers, config))
                .retrieve()
                .toEntity(String.class)
                .timeout(config.getTimeout())
                .doFinally(signal -> {
                    // 释放信号量
                    requestSemaphore.release();
                    logger.debug("[{}] 释放请求许可，当前可用许可: {}",
                            requestId, requestSemaphore.availablePermits());
                })
                .subscribe(
                        responseEntity -> {
                            // 成功回调：处理响应并发送到消息队列
                            handleSuccessResponse(requestId, responseEntity, config, startTime, mqMessageConfirm);
                        },
                        throwable -> {
                            // 错误回调：处理异常并发送错误结果到消息队列
                            handleErrorResponse(requestId, throwable, config, startTime, mqMessageConfirm);
                        }
                );

        // 立即返回，不等待异步请求完成
        return new AsyncRequestResult(requestId, true, "异步爬虫任务已启动");
    }

    /**
     * 执行异步POST爬虫请求并自动发送结果到消息队列
     */
    public AsyncRequestResult executeCrawlerPostAsync(String url, Object requestBody, RequestConfig config, MqMessageConfirm mqMessageConfirm) {
        String requestId = generateRequestId(config.getAirline(), config.getTaskType());
        long startTime = System.currentTimeMillis();

        // 1. 尝试获取信号量许可
        boolean acquired = false;
        try {
            acquired = requestSemaphore.tryAcquire(SEMAPHORE_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            if (!acquired) {
                logger.warn("[{}] 系统繁忙，无法获取请求许可，当前并发数: {}/{}",
                        requestId, getActiveRequestCount(), MAX_CONCURRENT_REQUESTS);
                recordSemaphoreRejection();
                return new AsyncRequestResult(requestId, false, "系统繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            logger.error("[{}] 获取请求许可被中断", requestId, e);
            Thread.currentThread().interrupt();
            return new AsyncRequestResult(requestId, false, "请求被中断");
        }

        logger.info("[{}] 开始异步POST爬虫请求: {}, 航司: {}, 任务类型: {}, 当前并发: {}/{}",
                requestId, url, config.getAirline(), config.getTaskType(),
                getActiveRequestCount(), MAX_CONCURRENT_REQUESTS);

        URI uri = buildUri(url, config);

        // 执行异步HTTP POST请求
        webClient.post()
                .uri(uri)
                .headers(headers -> addCustomHeaders(headers, config))
                .bodyValue(requestBody)
                .retrieve()
                .toEntity(String.class)
                .timeout(config.getTimeout())
                .doFinally(signal -> {
                    // 释放信号量
                    requestSemaphore.release();
                    logger.debug("[{}] 释放请求许可，当前可用许可: {}",
                            requestId, requestSemaphore.availablePermits());
                })
                .subscribe(
                        responseEntity -> {
                            // 成功回调：处理响应并发送到消息队列
                            handleSuccessResponse(requestId, responseEntity, config, startTime, mqMessageConfirm);
                        },
                        throwable -> {
                            // 错误回调：处理异常并发送错误结果到消息队列
                            handleErrorResponse(requestId, throwable, config, startTime, mqMessageConfirm);
                        }
                );

        // 立即返回，不等待异步请求完成
        return new AsyncRequestResult(requestId, true, "异步POST爬虫任务已启动");
    }

    /**
     * 在爬虫服务中使用的便捷POST方法
     */
    public AsyncRequestResult executeCrawlerPostForAirline(String url, Object requestBody, String airline, String taskType) {
        RequestConfig config = RequestConfig.builder()
                .timeout(Duration.ofSeconds(TIMEOUT))
                .airline(airline)
                .taskType(taskType)
                .headers(Map.of(
                        HttpHeaders.USER_AGENT, "Crawler-Bot/1.0",
                        HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE,
                        HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE
                ))
                .build();

        return executeCrawlerPostAsync(url, requestBody, config,new MqMessageConfirm());
    }
    /**
     * 在爬虫服务中使用的便捷get方法
     */
    public AsyncRequestResult executeCrawlerForAirline(String url, String airline, String taskType) {
        RequestConfig config = RequestConfig.builder()
                .timeout(Duration.ofSeconds(TIMEOUT))
                .airline(airline)
                .taskType(taskType)
                .headers(Map.of(
                        HttpHeaders.USER_AGENT, "Crawler-Bot/1.0",
                        HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE
                ))
                .build();

        return executeCrawlerAsync(url, config,new MqMessageConfirm());
    }

    /**
     * 处理成功响应
     */
    private void handleSuccessResponse(String requestId,
                                       ResponseEntity<String> responseEntity,
                                       RequestConfig config,
                                       long startTime,MqMessageConfirm mqMessageConfirm) {
        long responseTime = System.currentTimeMillis() - startTime;

        logger.info("[{}] 爬虫请求成功, 状态码: {}, 耗时: {}ms",
                requestId, responseEntity.getStatusCodeValue(), responseTime);

        try {
            // 构建爬虫结果对象
            Lcc_ZhengCe crawlerResult = buildCrawlerResult(config, responseEntity.getBody(), responseTime);

            // 发送到消息队列
            messageService.sendMessage(crawlerResult);
            logger.info("[{}] 爬虫结果已发送到消息队列, 航司: {}",
                    requestId, config.getAirline());
            recordRequestStats(true, responseTime);

        } catch (Exception e) {
            logger.error("[{}] 处理爬虫结果失败: {}", requestId, e.getMessage(), e);
            recordRequestStats(false, responseTime);
            //响应结果放入队列失败消息重新放入队列
            basicNackRetrun(mqMessageConfirm.getChannel(),mqMessageConfirm.getAmqpMessage(),mqMessageConfirm.getHangSi());
        }
    }

    /**
     * 处理错误响应
     */
    private void handleErrorResponse(String requestId,
                                     Throwable throwable,
                                     RequestConfig config,
                                     long startTime,MqMessageConfirm mqMessageConfirm) {
        long responseTime = System.currentTimeMillis() - startTime;

        String errorMessage;
        int statusCode = 0;

        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            statusCode = ex.getStatusCode().value();
            errorMessage = String.format("HTTP错误: %d - %s", statusCode, ex.getStatusText());
            logger.error("[{}] 爬虫请求失败: {}", requestId, errorMessage);
        } else if (throwable instanceof java.util.concurrent.TimeoutException) {
            logger.error("[{}] 爬虫请求超时", requestId);
        } else {
            errorMessage = throwable.getMessage();
            logger.error("[{}] 爬虫请求异常: {}", requestId, errorMessage, throwable);
        }
        recordRequestStats(false, responseTime);
        //响应结果获取失败消息重新放入队列
        basicNackRetrun(mqMessageConfirm.getChannel(),mqMessageConfirm.getAmqpMessage(),mqMessageConfirm.getHangSi());
    }

    /**
     * 构建爬虫结果对象
     */
    private Lcc_ZhengCe buildCrawlerResult(RequestConfig config, String responseBody, long responseTime) {
        Lcc_ZhengCe result = new Lcc_ZhengCe();
        result.setHangSi(config.getAirline());
        // 根据实际业务设置
        result.setProductType(1);
        result.setOriginalCurrency("爬虫数据 - 响应时间: " + responseTime + "ms");
        // 这里可以根据实际响应内容设置更多字段
        // result.setSomeField(parseResponse(responseBody));
        return result;
    }

    /**
     * 构建错误结果对象
     */
    private Lcc_ZhengCe buildErrorResult(RequestConfig config, String errorMessage, int statusCode, long responseTime) {
        Lcc_ZhengCe result = new Lcc_ZhengCe();
        result.setHangSi(config.getAirline());
        result.setProductType(1);
        result.setOriginalCurrency("爬虫失败 - 错误: " + errorMessage + ", 状态码: " + statusCode);
        return result;
    }



    // 私有辅助方法
    private URI buildUri(String url, RequestConfig config) {
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(url);

        if (config.getQueryParams() != null) {
            config.getQueryParams().forEach(uriBuilder::queryParam);
        }

        return uriBuilder.build().toUri();
    }

    private void addCustomHeaders(HttpHeaders headers, RequestConfig config) {
        if (config.getHeaders() != null) {
            config.getHeaders().forEach(headers::add);
        }
    }

    private String generateRequestId(String airline, String taskType) {
        String safeTaskType = (taskType == null || taskType.trim().isEmpty()) ? "UNKNOWN" : taskType;
        return String.format("CRAWL-%s-%s-%d-%d",
                airline, safeTaskType, System.currentTimeMillis(), requestCounter.incrementAndGet());
    }

    private void recordRequestStats(boolean success, long responseTime) {
        String statsKey = "global";
        RequestStats stats = requestStats.computeIfAbsent(statsKey, k -> new RequestStats());

        stats.totalRequests++;
        stats.totalResponseTime += responseTime;

        if (success) {
            stats.successRequests++;
        } else {
            stats.failedRequests++;
        }
    }
    /**
     * 获取当前活跃请求数量
     */
    public int getActiveRequestCount() {
        return MAX_CONCURRENT_REQUESTS - requestSemaphore.availablePermits();
    }

    /**
     * 获取信号量状态
     */
    public Map<String, Object> getSemaphoreStatus() {
        return Map.of(
                "maxConcurrentRequests", MAX_CONCURRENT_REQUESTS,
                "availablePermits", requestSemaphore.availablePermits(),
                "activeRequests", getActiveRequestCount(),
                "queueLength", requestSemaphore.getQueueLength()
        );
    }

    /**
     * 记录信号量拒绝统计
     */
    private void recordSemaphoreRejection() {
        String statsKey = "global";
        RequestStats stats = requestStats.computeIfAbsent(statsKey, k -> new RequestStats());
        stats.semaphoreRejections++;
    }

    /**
     * 获取请求统计信息
     */
    public Map<String, Object> getRequestStats() {
        RequestStats stats = requestStats.get("global");
        if (stats == null) {
            return Map.of(
                    "totalRequests", 0,
                    "successRequests", 0,
                    "failedRequests", 0,
                    "successRate", 0,
                    "averageResponseTime", 0,
                    "semaphoreRejections", 0,
                    "activeRequests", getActiveRequestCount(),
                    "maxConcurrentRequests", MAX_CONCURRENT_REQUESTS
            );
        }

        long avgResponseTime = stats.totalRequests > 0 ? stats.totalResponseTime / stats.totalRequests : 0;

        return Map.of(
                "totalRequests", stats.totalRequests,
                "successRequests", stats.successRequests,
                "failedRequests", stats.failedRequests,
                "successRate", stats.totalRequests > 0 ?
                        (double) stats.successRequests / stats.totalRequests : 0,
                "averageResponseTime", avgResponseTime,
                "semaphoreRejections", stats.semaphoreRejections,
                "activeRequests", getActiveRequestCount(),
                "maxConcurrentRequests", MAX_CONCURRENT_REQUESTS
        );
    }
    /**
     * 使用示例
     */
    public static void main(String[] args) throws Exception {
        // 模拟MessageService（实际使用时通过Spring注入）
        MessageService messageService = new MessageService() {
            @Override
            public void sendMessage(Lcc_ZhengCe message) {

            }

            @Override
            public void sendMessage(SaasSysRequestMessage message) {

            }
        };
        AsyncHttpClientWithMQ client = new AsyncHttpClientWithMQ(messageService);

        // GET请求示例 - 爬取航司政策
        System.out.println("=== GET请求示例 ===");
        AsyncHttpClientWithMQ.AsyncRequestResult getResult = client.executeCrawlerForAirline(
                "https://api.airline.com/policies",
                "MU",
                "POLICY"
        );
        System.out.println("GET请求已提交: " + getResult.getRequestId());

        // POST请求示例 - 提交查询条件
        System.out.println("\n=== POST请求示例 ===");
        Map<String, Object> postData = Map.of(
                "departure", "PEK",
                "arrival", "SHA",
                "date", "2025-11-10",
                "passengers", Map.of("adult", 1)
        );

        AsyncHttpClientWithMQ.AsyncRequestResult postResult = client.executeCrawlerPostForAirline(
                "https://api.airline.com/search/prices",
                postData,
                "CA",
                "PRICE_SEARCH"
        );
        System.out.println("POST请求已提交: " + postResult.getRequestId());

        // 使用完整配置的POST请求
        System.out.println("\n=== 完整配置POST请求示例 ===");
        Map<String, Object> complexData = Map.of(
                "searchCriteria", Map.of(
                        "routes", new String[]{"PEK-SHA", "SHA-PEK"},
                        "dates", new String[]{"2025-11-10", "2025-11-15"},
                        "cabinClass", "ECONOMY"
                ),
                "options", Map.of(
                        "includeBaggage", true,
                        "includeMeals", false
                )
        );

        AsyncHttpClientWithMQ.RequestConfig complexConfig = AsyncHttpClientWithMQ.RequestConfig.builder()
                .timeout(Duration.ofSeconds(180))
                .airline("CZ")
                .taskType("COMPLEX_SEARCH")
                .headers(Map.of(
                        "Content-Type", "application/json",
                        "X-API-Key", "your-api-key-here",
                        "User-Agent", "Crawler-Bot/2.0"
                ))
                .queryParams(Map.of("cache", "false"))
                .build();

        AsyncHttpClientWithMQ.AsyncRequestResult complexResult = client.executeCrawlerPostAsync(
                "https://api.airline.com/advanced/search",
                complexData,
                complexConfig,
                new MqMessageConfirm()
        );
        System.out.println("复杂POST请求已提交: " + complexResult.getRequestId());

        // 等待异步任务执行
        Thread.sleep(3000);

        // 查看统计信息
        System.out.println("\n=== 爬虫统计信息 ===");
        System.out.println("统计: " + client.getRequestStats());
    }
}