package com.llq.hs.service.crawler.mq.producer;



import com.llq.hs.service.crawler.mq.rep.Lcc_ZhengCe;
import com.llq.hs.service.crawler.mq.req.SaasSysRequestMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/**
 * Description:生产者服务
 * <AUTHOR>
 */
@Service
public class MessageServiceImpl implements MessageService{
    private static final Logger logger = LoggerFactory.getLogger(MessageServiceImpl.class);

    //交换机名称
    @Value("${rabbitConfig.exchangeName}")
    private  String exchangeName;
    @Value("${rabbitConfig.producer.routingKey}")
    private String producerRoutingKey;

    @Value("${rabbitConfig.producerRep.queueName}")
    private  String producerRepQueueName;
    @Value("${rabbitConfig.producerRep.routingKey}")
    private String producerRepRoutingKey;

    private final RabbitTemplate rabbitTemplate;
    public MessageServiceImpl(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }


    /**
     * @description: 获取爬虫结果并进行结果返回，将响应结果放入消息队列
     * @param: message
     * @return: void
     * <AUTHOR>
     * @date: 2025/11/3 11:37
     */
    @Override
    public void sendMessage(Lcc_ZhengCe message){
        try {
            // 发送消息到队列
            rabbitTemplate.convertAndSend(
                    exchangeName,
                    producerRepRoutingKey,
                    message
            );
            logger.info("消息已成功发送到爬虫结果队列: {}", message);
        } catch (Exception e) {
            logger.error("发送消息到爬虫结果队列时发生错误: {}", e.getMessage());
        }

        }

    @Override
    public void sendMessage(SaasSysRequestMessage message){
        try {
            // 发送消息到队列
            rabbitTemplate.convertAndSend(
                    exchangeName,
                    producerRoutingKey,
                    message
            );
            logger.info("消息已成功发送到队列: {}", message);
        } catch (Exception e) {
            logger.error("发送消息到队列时发生错误: {}", e.getMessage());
        }

    }

}
