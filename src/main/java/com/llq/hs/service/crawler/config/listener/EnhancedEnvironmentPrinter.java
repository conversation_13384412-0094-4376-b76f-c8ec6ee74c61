package com.llq.hs.service.crawler.config.listener;

import com.llq.hs.service.crawler.dispatch.crawler.CrawlerDispatchCenter;
import com.llq.hs.service.crawler.dispatch.crawler.service.BaseCrawlerService;
import com.llq.hs.service.crawler.dispatch.hangsi.HangSiDispatchCenter;
import com.llq.hs.service.crawler.dispatch.hangsi.service.BaseAirlineService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.stereotype.Component;

import java.lang.management.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: 环境信息打印（包含组件注册信息）
 * @date 2025/11/4
 */
@Component
public class EnhancedEnvironmentPrinter implements ApplicationRunner {
    private static final Logger logger = LoggerFactory.getLogger(EnhancedEnvironmentPrinter.class);

    private final ConfigurableEnvironment environment;
    private final HangSiDispatchCenter hangSiDispatchCenter;
    private final CrawlerDispatchCenter crawlerDispatchCenter;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public EnhancedEnvironmentPrinter(ConfigurableEnvironment environment,
                                      HangSiDispatchCenter hangSiDispatchCenter,
                                      CrawlerDispatchCenter crawlerDispatchCenter) {
        this.environment = environment;
        this.hangSiDispatchCenter = hangSiDispatchCenter;
        this.crawlerDispatchCenter = crawlerDispatchCenter;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 等待应用完全启动
        Thread.sleep(1000);
        printEnhancedEnvironmentInfo();
    }


    private void printEnhancedEnvironmentInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("\n").append("=".repeat(80)).append("\n");
        sb.append("🚀 爬虫服务系统启动成功\n");
        sb.append("=".repeat(80)).append("\n");

        // 应用配置信息
        printApplicationInfo(sb);

        // 组件注册信息
        printComponentRegistrationInfo(sb);

        // 数据源配置
        printDataSourceInfo(sb);

        // 消息队列配置
        printMessageQueueInfo(sb);

        // JVM信息
        printJvmInfo(sb);

        // 系统资源信息
        printSystemResourceInfo(sb);

        sb.append("=".repeat(80)).append("\n");

        logger.info(sb.toString());
    }

    private void printApplicationInfo(StringBuilder sb) {
        sb.append("🔧 应用配置信息:\n");
        sb.append("   ▪ 应用名称: ").append(environment.getProperty("spring.application.name", "crawler-service")).append("\n");
        sb.append("   ▪ 服务端口: ").append(environment.getProperty("server.port", "8080")).append("\n");
        sb.append("   ▪ 上下文路径: ").append(environment.getProperty("server.servlet.context-path", "/")).append("\n");
        sb.append("   ▪ 激活环境: ").append(getActiveProfilesString()).append("\n");
        sb.append("   ▪ 默认环境: ").append(String.join(", ", environment.getDefaultProfiles())).append("\n");
        sb.append("   ▪ 工作目录: ").append(System.getProperty("user.dir")).append("\n");
        sb.append("\n");
    }

    private void printComponentRegistrationInfo(StringBuilder sb) {
        sb.append("📦 组件注册信息:\n");

        // 航司服务注册信息
        int airlineServiceCount = hangSiDispatchCenter.getRegisteredServiceCount();
        sb.append("   ▪ 航司服务 (").append(airlineServiceCount).append(" 个):\n");

        // 获取详细的航司服务信息
        Map<String, BaseAirlineService> airlineServices = getAirlineServices();
        if (!airlineServices.isEmpty()) {
            airlineServices.forEach((code, service) -> {
                sb.append("     - ").append(code).append(": ").append(service.getClass().getSimpleName()).append("\n");
            });
        } else {
            sb.append("     - 无注册的航司服务\n");
        }

        // 爬虫服务注册信息
        Map<String, BaseCrawlerService> crawlerServices = crawlerDispatchCenter.getCrawlerServices();
        sb.append("   ▪ 爬虫服务 (").append(crawlerServices.size()).append(" 个):\n");
        if (!crawlerServices.isEmpty()) {
            crawlerServices.forEach((key, service) -> {
                String[] parts = key.split("_");
                if (parts.length == 2) {
                    sb.append("     - ").append(parts[0]).append(".").append(parts[1])
                            .append(": ").append(service.getClass().getSimpleName()).append("\n");
                } else {
                    sb.append("     - ").append(key).append(": ").append(service.getClass().getSimpleName()).append("\n");
                }
            });
        } else {
            sb.append("     - 无注册的爬虫服务\n");
        }

        // 线程池信息（从环境变量获取或硬编码）
        sb.append("   ▪ 线程池配置:\n");
        sb.append("     - 核心线程: ").append(environment.getProperty("thread.task.core", "未配置")).append("\n");
        sb.append("     - 最大线程: ").append(environment.getProperty("thread.task.max", "未配置")).append("\n");
        sb.append("     - 队列容量: ").append(environment.getProperty("thread.task.queue", "未配置")).append("\n");

        sb.append("\n");
    }

    private void printDataSourceInfo(StringBuilder sb) {
        sb.append("🗄️  数据源配置:\n");
        String url = environment.getProperty("spring.datasource.url");
        if (url != null) {
            // 简化的URL显示，隐藏密码
            String safeUrl = url.replaceAll("://([^:]+):[^@]+@", "://***@");
            sb.append("   ▪ 数据库URL: ").append(safeUrl).append("\n");
        }
        sb.append("   ▪ 数据库用户: ").append(environment.getProperty("spring.datasource.username", "未配置")).append("\n");
        sb.append("   ▪ 连接池类型: ").append(environment.getProperty("spring.datasource.type", "HikariCP")).append("\n");
        sb.append("   ▪ 最大连接数: ").append(environment.getProperty("spring.datasource.hikari.maximum-pool-size", "10")).append("\n");
        sb.append("   ▪ 最小空闲连接: ").append(environment.getProperty("spring.datasource.hikari.minimum-idle", "5")).append("\n");
        sb.append("\n");
    }

    private void printMessageQueueInfo(StringBuilder sb) {
        sb.append("🐰 消息队列配置:\n");
        sb.append("   ▪ RabbitMQ主机: ").append(environment.getProperty("spring.rabbitmq.host", "localhost")).append("\n");
        sb.append("   ▪ RabbitMQ端口: ").append(environment.getProperty("spring.rabbitmq.port", "5672")).append("\n");
        sb.append("   ▪ RabbitMQ虚拟主机: ").append(environment.getProperty("spring.rabbitmq.virtual-host", "/")).append("\n");
        sb.append("   ▪ RabbitMQ用户名: ").append(environment.getProperty("spring.rabbitmq.username", "guest")).append("\n");
        sb.append("   ▪ 消费者并发: ").append(environment.getProperty("spring.rabbitmq.listener.simple.concurrency", "未配置")).append("\n");
        sb.append("   ▪ 最大消费者: ").append(environment.getProperty("spring.rabbitmq.listener.simple.max-concurrency", "未配置")).append("\n");
        sb.append("\n");
    }

    private void printBusinessConfig(StringBuilder sb) {
        sb.append("🎯 业务配置:\n");
        appendIfPresent(sb, "   ▪ LCC开关: ", "lcc.vj.switch");
        appendIfPresent(sb, "   ▪ 邮件服务: ", "lcc.mail.server");
        appendIfPresent(sb, "   ▪ 邮件路径: ", "lcc.mail.path");
        appendIfPresent(sb, "   ▪ 爬虫超时: ", "crawler.timeout");
        appendIfPresent(sb, "   ▪ 重试次数: ", "crawler.retry.count");
        sb.append("\n");
    }

    private void printJvmInfo(StringBuilder sb) {
        RuntimeMXBean runtimeMxBean = ManagementFactory.getRuntimeMXBean();
        MemoryMXBean memoryMxBean = ManagementFactory.getMemoryMXBean();
        OperatingSystemMXBean osMxBean = ManagementFactory.getOperatingSystemMXBean();
        ThreadMXBean threadMxBean = ManagementFactory.getThreadMXBean();
        CompilationMXBean compilationMxBean = ManagementFactory.getCompilationMXBean();

        sb.append("📊 JVM详细信息:\n");

        // 启动和运行时间（修复版）
        long startTime = runtimeMxBean.getStartTime();
        long uptime = runtimeMxBean.getUptime();
        sb.append("   ▪ 启动时间: ").append(dateFormat.format(new Date(startTime))).append("\n");
        sb.append("   ▪ 运行时间: ").append(formatUptime(uptime)).append("\n");

        // JVM基础信息
        sb.append("   ▪ JVM名称: ").append(runtimeMxBean.getVmName()).append("\n");
        sb.append("   ▪ JVM版本: ").append(runtimeMxBean.getVmVersion()).append("\n");
        sb.append("   ▪ JVM厂商: ").append(runtimeMxBean.getVmVendor()).append("\n");

        // 简化的启动参数（只显示前3个）
        List<String> inputArgs = runtimeMxBean.getInputArguments();
        if (!inputArgs.isEmpty()) {
            sb.append("   ▪ JVM启动参数: ");
            int count = Math.min(inputArgs.size(), 3);
            for (int i = 0; i < count; i++) {
                if (i > 0) sb.append(" ");
                sb.append(inputArgs.get(i));
            }
            if (inputArgs.size() > 3) {
                sb.append(" ... (共").append(inputArgs.size()).append("个参数)");
            }
            sb.append("\n");
        }

        // 内存信息
        MemoryUsage heapMemory = memoryMxBean.getHeapMemoryUsage();
        MemoryUsage nonHeapMemory = memoryMxBean.getNonHeapMemoryUsage();
        sb.append("   ▪ 堆内存: ").append(formatMemory(heapMemory.getUsed()))
                .append(" / ").append(formatMemory(heapMemory.getMax())).append("\n");
        sb.append("   ▪ 非堆内存: ").append(formatMemory(nonHeapMemory.getUsed()))
                .append(" / ").append(formatMemory(nonHeapMemory.getMax())).append("\n");

        // 线程信息
        sb.append("   ▪ 活动线程: ").append(threadMxBean.getThreadCount())
                .append(" / 峰值: ").append(threadMxBean.getPeakThreadCount()).append("\n");
        sb.append("   ▪ 守护线程: ").append(threadMxBean.getDaemonThreadCount()).append("\n");

        // 编译信息
        if (compilationMxBean.isCompilationTimeMonitoringSupported()) {
            sb.append("   ▪ JIT编译器: ").append(compilationMxBean.getName())
                    .append(" (编译时间: ").append(compilationMxBean.getTotalCompilationTime()).append("ms)\n");
        }

        // 垃圾收集器信息
        printGarbageCollectorInfo(sb);

        sb.append("\n");
    }

    private void printGarbageCollectorInfo(StringBuilder sb) {
        List<GarbageCollectorMXBean> gcBeans = ManagementFactory.getGarbageCollectorMXBeans();
        if (!gcBeans.isEmpty()) {
            sb.append("   ▪ 垃圾收集器:\n");
            for (GarbageCollectorMXBean gcBean : gcBeans) {
                sb.append("     - ").append(gcBean.getName())
                        .append(": 次数=").append(gcBean.getCollectionCount())
                        .append(", 时间=").append(gcBean.getCollectionTime()).append("ms\n");
            }
        }
    }

    private void printSystemResourceInfo(StringBuilder sb) {
        OperatingSystemMXBean osMxBean = ManagementFactory.getOperatingSystemMXBean();
        Runtime runtime = Runtime.getRuntime();

        sb.append("💻 系统资源信息:\n");
        sb.append("   ▪ 操作系统: ").append(osMxBean.getName()).append(" ").append(osMxBean.getVersion()).append("\n");
        sb.append("   ▪ 系统架构: ").append(osMxBean.getArch()).append("\n");
        sb.append("   ▪ 可用处理器: ").append(runtime.availableProcessors()).append(" 核心\n");
        sb.append("   ▪ 系统负载: ").append(String.format("%.2f", osMxBean.getSystemLoadAverage())).append("\n");
        sb.append("   ▪ 最大内存: ").append(formatMemory(runtime.maxMemory())).append("\n");
        sb.append("   ▪ 总内存: ").append(formatMemory(runtime.totalMemory())).append("\n");
        sb.append("   ▪ 空闲内存: ").append(formatMemory(runtime.freeMemory())).append("\n");
        sb.append("   ▪ 已用内存: ").append(formatMemory(runtime.totalMemory() - runtime.freeMemory())).append("\n");
        sb.append("\n");
    }

    private String getActiveProfilesString() {
        String[] activeProfiles = environment.getActiveProfiles();
        return activeProfiles.length > 0 ? String.join(", ", activeProfiles) : "default";
    }

    private String formatUptime(long uptime) {
        long days = TimeUnit.MILLISECONDS.toDays(uptime);
        long hours = TimeUnit.MILLISECONDS.toHours(uptime) % 24;
        long minutes = TimeUnit.MILLISECONDS.toMinutes(uptime) % 60;
        long seconds = TimeUnit.MILLISECONDS.toSeconds(uptime) % 60;

        if (days > 0) {
            return String.format("%d天 %02d:%02d:%02d", days, hours, minutes, seconds);
        } else {
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
        }
    }

    private String formatMemory(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    private void appendIfPresent(StringBuilder sb, String prefix, String property) {
        String value = environment.getProperty(property);
        if (value != null) {
            sb.append(prefix).append(value).append("\n");
        }
    }

    /**
     * 获取航司服务详情
     */
    private Map<String, BaseAirlineService> getAirlineServices() {
        // 这里需要通过反射获取HangSiDispatchCenter内部的airlineServiceMap
        // 或者修改HangSiDispatchCenter提供获取方法
        try {
            // 通过反射获取私有字段
            java.lang.reflect.Field field = HangSiDispatchCenter.class.getDeclaredField("airlineServiceMap");
            field.setAccessible(true);
            @SuppressWarnings("unchecked")
            Map<String, BaseAirlineService> services = (Map<String, BaseAirlineService>) field.get(hangSiDispatchCenter);
            return services;
        } catch (Exception e) {
            // 如果反射失败，返回空Map
            return java.util.Collections.emptyMap();
        }
    }
}