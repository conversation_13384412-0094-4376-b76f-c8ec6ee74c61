package com.llq.hs.service.crawler.VJ.service;


import com.llq.hs.service.crawler.VJ.dto.VjResultDto;
import com.llq.hs.service.crawler.VJ.vo.VjApiResultVo;

/**
 * <AUTHOR>
 * @version 2025/07/08
 **/
public interface Lcc_VjGetDataService {



    /**
     * 获取接口数据
     * @param depCity
     * @param arrCity
     * @param departureDate
     * @param currency
     * @param seq
     * @param depAirport
     * @param arrAirport
     * @return
     */
    VjResultDto getOneWayVjTicketData(String depCity, String arrCity, String departureDate, String currency, int seq, String depAirport, String arrAirport);

    VjResultDto getOneWayVjTicketData(String depCity, String arrCity, String departureDate, String currency, VjApiResultVo resultVo);
}
