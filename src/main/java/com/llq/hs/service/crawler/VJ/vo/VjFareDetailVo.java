package com.llq.hs.service.crawler.VJ.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2025/09/12
 **/
@Data
public class VjFareDetailVo {


    /**
     * AoTQBurpMIHnf9uG81eƒlnbbO36ua8J7KOmzopCg3qbaiV4Kjs2UT69rvfalbNoqPOXBeGTDVLL05VYdi5cDaQei5pyi¥wmF1sKfS¥MuqBftfPxzuj¥D3tJgvZTmFWaBaztCƒ4pJmsPzHBgaYMwCMUcx3ZHlpV5syii7Wxc3YmoNqBjSQXXEyWuqlulqGLvmZMgpkmnqDHkSiHJ2UDyrQ6Ryg9¥8Jxbs¥MzUWEb1D8Dzkg4x10ƒxLs2sxWvCeaxh23zW3Fi5kƒ¥3CNrvAT9bsB¥lI4ksdSZB¥KOqEq2ZTkdqAtwRwz3Cyƒ26divzcjVGmxB5nyJvhyfBnoPR3h5mWlPsFZUp9V8JXW803ofEu0gJNCwSuqIcAHbcHZvZk5J¥8HQlBPb46¥yb¥mLtDqwCKrXD¥5FoWuPzgXOlZNF4kCaMzVj5vOs0g1FPdzeyaObjga38WgzIczniivQgrfD8mZcdG5KW1qMIkVYTN6Cqu¥7ƒqvTT4NGncOAmCQFG0dhSi7IHq2acsOPNH8KdJJwfhi0h72OEPSKzCOQ2Tj7p¥AD¥kR7cy0lG0yzƒmmZrNm1L
     */
    private String bookingKey;


    /**
     * 1608.45
     */
    private BigDecimal totalFare;

    /**
     * 0
     */
    private Integer totalDiscount;

    /**
     * 0
     */
    private Integer allFareWithoutBag;

    /**
     * 1251.9
     */
    private BigDecimal totalAdultFare;

    /**
     * 1251.9
     */
    private Integer totalChildFare;

    /**
     * 1251.9
     */
    private Integer totalInfantFare;

    /**
     * 1
     */
    private Integer adultCount;

    /**
     * 0
     */
    private Integer childCount;

    /**
     * 0
     */
    private Integer infantCount;


    /**
     * false
     */
    private Boolean priceIncludesTax;

    /**
     * 1170
     */
    private Integer basePrice;


    /**
     * 1251.9
     */
    private BigDecimal adultFare;


    /**
     * 1251.9
     */
    private BigDecimal adult_price;


    /**
     * THB
     */
    private String currency_code;

    /**
     * 0
     */
    private Integer childFare;

    /**
     * 0
     */
    private Integer infantFare;


    /**
     * 96.72
     */
    private BigDecimal tax;


    /**
     *
     * Eco
     */
    private String name;

    /**
     *
     * Eco
     */
    private String code;

    /**
     * html内容
     */
    private String content;

    /**
     *
     * 4
     */
    private Integer order;

    /**
     *
     * 18
     */
    private Integer max_passengers;


    /**
     *
     */
    private VjPriceDetailVo price_detail;


    /**
     *
     * 1,608.45
     */
    private String price_display_select_fare;

    /**
     *
     * 1,608.45
     */
    private String totalBeforeDiscount;

    /**
     *
     * 1
     */
    private Integer availability;


}
