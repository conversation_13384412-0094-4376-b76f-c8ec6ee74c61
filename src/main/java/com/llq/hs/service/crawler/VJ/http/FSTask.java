package com.llq.hs.service.crawler.VJ.http;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static com.llq.hs.service.crawler.VJ.http.VjHttpUtil.readHtmlContentFromEntity;
import static com.llq.hs.service.crawler.VJ.util.TLSFingerprintHttpClient.createHttpClientNew;

public class FSTask {

    static Logger logger = LoggerFactory.getLogger(FSTask.class);

    String proxy_host = "";
    int proxy_post = 0;
    String proxy_user = "";
    String proxy_pass = "";

    public static void main(String[] args) {
        String a = "{\n" +
                "    \"air_type\": \"S7\",\n" +
                "    \"data\": {\n" +
                "        \"searchParams\": {\n" +
                "            \"currency\": \"KZT\",\n" +
                "            \"directOnly\": false,\n" +
                "            \"ownAirlineOnly\": false,\n" +
                "            \"passengersAmount\": {\n" +
                "                \"adults\": 1,\n" +
                "                \"children\": 0,\n" +
                "                \"infants\": 0\n" +
                "            },\n" +
                "            \"promoCode\": \"\",\n" +
                "            \"redemption\": false,\n" +
                "            \"routes\": [\n" +
                "                {\n" +
                "                    \"departureDate\": \"2025-08-" + "22" + "\",\n" +
                "                    \"destination\": \"AKX\",\n" +
                "                    \"origin\": \"SCO\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"searchType\": \"EXACT\",\n" +
                "            \"subsidizedPassengerTypes\": [],\n" +
                "            \"tripType\": \"ONE_WAY\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"token\": \"\"\n" +
                "}";
        // 提交10个任务
        ExecutorService executor = Executors.newFixedThreadPool(10);

        AtomicInteger counter = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(10);

//        for (int j = 0; j < 2; j++) {
        FSTask s7Task = new FSTask();
//            final int taskId = j + 1;
//            executor.submit(() -> {
//                try {
//                    System.out.println("线程 " + taskId + " 开始执行 - " + Thread.currentThread().getName());
//                    // 模拟业务处理
//                    s7Task.run(a);
//                    int count = counter.incrementAndGet();
//                    System.out.println("线程 " + taskId + " 执行完成，已完成: " + count);
//                } catch (Exception e) {
//                    Thread.currentThread().interrupt();
//                } finally {
//                    latch.countDown();
//                }
//            });
//        }
//        s7Task.runFlight();
        s7Task.run(a);
    }

    public String run(String requestInfo) {

        JSONObject jsonObject = JSONObject.parseObject(requestInfo);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONObject searchParams = data.getJSONObject("searchParams");
        JSONObject passengersAmount = searchParams.getJSONObject("passengersAmount");
        String cur = searchParams.getString("currency");

        int adults = passengersAmount.getIntValue("adults");
        int children = passengersAmount.getIntValue("children");
        int infants = passengersAmount.getIntValue("infants");

        JSONObject route = searchParams.getJSONArray("routes").getJSONObject(0);
        String departureDate = route.getString("departureDate");
        String destination = route.getString("destination");
        String origin = route.getString("origin");

//        DefaultHttpClient client = S7HttpUtil.getHttpClient("");

        String getProxy = QueueUtil.poll();

        if (Strings.isNullOrEmpty(getProxy)) {
            return "ERROR_GET_IP_NULL";
        }

//            for (int i = 0; i < 3; i++) {
//                if (getProxy.contains("ERROR_")) {
//                    getProxy = getProxyIP(client);
//                }
//            }
//            String[] split = getProxy.split("\r\n");
//            for (String s : split) {
//                ipList.offer(s);
//            }
//        }
//
//
//        String ip = ipList.poll(); // 取出队首元素
//        logger.info("获取带的代理:" + getProxy);
        String s = getProxy.split(",")[0];
        String[] split = s.split(":");

        proxy_host = split[0];
        proxy_post = Integer.parseInt(split[1].replaceAll("\r", "").replaceAll("\n", ""));
        proxy_user = "luluqi";
        proxy_pass = "luluqi";

        String ip_proxy = getProxy.split(",")[1];

        String proxy_str = proxy_host + ":" + proxy_post + "@" + proxy_user + ":" + proxy_pass;
        logger.info("本次请求代理:" + proxy_str);
//        proxy_str = "https://" + proxy_user + ":" + proxy_pass + "@" + proxy_host + ":" + proxy_post;
//        proxy_str = "http://" + proxy_host + ":" + proxy_post + "@" + proxy_user + ":" + proxy_pass;
        CloseableHttpClient client = createHttpClientNew(proxy_host, proxy_post, proxy_user, proxy_pass);

//        String calendar = calendar(client, origin, destination, departureDate);
        String search = search(client, origin, destination, departureDate, adults, children, infants, cur);
        return search;
    }

    private String calendar(CloseableHttpClient client, String origin, String destination, String departureDate) {
        String[] split = departureDate.split("-");
        String url = "https://booking.flyarystan.com/ibe/search/availableFlightDates?depPort="+origin+"&arrPort="+destination+"&startDate="+ split[2] + "." + split[1] + "." + split[0];
        HttpGet httpGet = new HttpGet(url);

        httpGet.setHeader("Host", "booking.flyarystan.com");
//        httpGet.setHeader("Cookie", "JSESSIONID=6866ABD8B272E91D2B061ABDF43AA77A; visid_incap_1880816=ES/fc8YbSNukZy0TeCK+YEA1nGgAAAAAQUIPAAAAAADgDTmiBmt/T+Q+v/YDaBmW; nlbi_1880816=f9VeO51rEG4F6kuhxygCuQAAAAAjAKuWpYubnPWh8xzZZWr+; visid_incap_3179598=3urgTympQiWyLhMMlDfa6UE1nGgAAAAAQUIPAAAAAACRpcT5t7//dZS1OZ1KUEYa; GCLB=CLi7mLKMnMeLnAEQAw; nlbi_3179598=KYvmDXN2EgRbicL7feSt1AAAAACQIKJ0SCN0Q7wmCbnxXMcl; AMP_MKTG_d45dd76c2e=JTdCJTdE; _gcl_au=1.1.921488106.1755067791; _ym_uid=1755067792299308896; _ym_d=1755067792; incap_ses_1826_1880816=ZnUyGAqUaREp7ft5sEFXGVVDnGgAAAAABMVJBDkN9dLgLLC/92nEXg==; incap_ses_1369_1880816=CCUKXgFg/U7oBUX5oKr/EsNfnGgAAAAAWiMVDHOf5mXp5NfcmiUywg==; incap_ses_1833_1880816=YekeRbnvKQhbhVn5JiBwGc9mnGgAAAAAQytJGfERaxX9b6oWFEN9ag==; incap_ses_7229_1880816=nO81XdCCLWa2ys51kpFSZJ2FnGgAAAAAKgIUtPAbW5rRDEo1kJDzmw==; incap_ses_2107_1880816=PH3za38b83NZVyu2tZE9HSKJnGgAAAAAqjxzP8WglozDEM3Zk/sj5A==; incap_ses_1002_1880816=ngGsN35kRAwEEOAaDtLnDSdHnWgAAAAAxbCyuVToynVf7NBhfpnukw==; _ym_isad=2; _fbp=fb.1.1755139902019.573578595223696519; cookieyes-consent=consentid:QzRYbzR1c1JEUEVTOVNOcDJocnZ5UlcyUWdvcVpKTko,consent:yes,action:no,necessary:yes,functional:yes,analytics:yes,performance:yes,advertisement:yes; ckIBEpersist=!02ihwQz3wvh80+NslCzSo16hMMp6am09uXotSlEKpc+NksY5dFco/u5OJD0kY2iv756kxJxXvMkl9g==; nlbi_3179598_2147483392=r2JaLRZ98AxysxcefeSt1AAAAACcMt3h3f33Hp1TB9SXyDDr; reese84=3:kBRozrPb2ez8OEU/At8mkw==:aaksCTwhNHa5qBbr+3wYqGdQjs+YOsD8gWgbr/a1jTMuGdz3OBqKpX1B/S797TEcA74+GIQa3PXRvOf/qzzxAY+8AQ/+TWA+xK1Jrq0eKT+MOtxco7m4GTQ47ZNiWZ1fNymb8zE2HWHM6+bBqfCTkvzp8G75JkLUDw0/4gKx2CVCQHRuQVXyscpQ/cX2JAU08R7za7EUz+J1i4c4sf5lgQh8WJCw4Um/DdHXfr6AL2WdSEcO9XmMykSjs8/EMSE8Pb/DtLIyjA4Fk6CBlvMISqyog/R3yJo4xY7wV+iiHWDD1knPgQCNTLtwibc6SH4ixIiJSsyjFtl/O8rSEFv2KOaPAKH222TjUOqqs8zTmtSbjj/9UyKvzU7P2UG2UHa632d49SL4V9rE6gzDHXnrgvwMqNIEj833XiskTF4Pkl0eSPpG4Zw+UM4ObeOsXlA02WnVU1rDqduAaKQBGA3eng==:WSLiI6OVDHs7u22CKS10Zly6GkUrWQ0uCsXQMaKzNjM=; incap_ses_8078_1880816=5DVnEAcAu1eySf2didIacMuBnWgAAAAAY3TeqwiO4MVw7ZfYVvY68Q==; ARRAffinitySameSite=0bc99887f10809c8a4fbce625f6790c7807de861f12abc25e7ad210960a9aa25; incap_ses_228_1880816=OnIuXZYyyxc4qc3sNQUqA7iCnWgAAAAApxNI2dzalPIBJF/tXgC8YQ==; incap_ses_1448_3179598=1AdSR5Q3uk0FSyY7nFQYFFKFnWgAAAAAE6MCfsI9WVzwQSqJ37hU5w==; nlbi_1880816_2147483392=71hzaQlCk1QjMRbrxygCuQAAAACTjvetbNjHN8/6+PcJ/jJj; _gid=GA1.2.1027239725.1755153750; _ga_75PYZFVBX6=GS2.1.s1755153748" + System.getenv("o1") + System.getenv("g0") + System.getenv("t1755153748") + System.getenv("j60") + System.getenv("l0") + System.getenv("h0") + "; _ga=GA1.1.980050143.1755153750; _ga_FYLXGW40XL=GS2.1.s1755153748" + System.getenv("o1") + System.getenv("g0") + System.getenv("t1755153748") + System.getenv("j60") + System.getenv("l0") + System.getenv("h0") + "; _ym_visorc=w; ARRAffinity=0bc99887f10809c8a4fbce625f6790c7807de861f12abc25e7ad210960a9aa25; AMP_d45dd76c2e=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjJkYjdmZTkzMC1mZGQ2LTRjNjAtYmYzMS1jODkwMmMyZGFjNWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzU1MTUzNzQ4NDE3JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJsYXN0RXZlbnRUaW1lJTIyJTNBMTc1NTE1MzgyODQyNCUyQyUyMmxhc3RFdmVudElkJTIyJTNBNiU3RA==")
        httpGet.setHeader("sec-ch-ua", "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"");
        httpGet.setHeader("sec-ch-ua-mobile", "?0");
        httpGet.setHeader("sec-ch-ua-platform", "\"macOS\"");
        httpGet.setHeader("upgrade-insecure-requests", "1");
        httpGet.setHeader("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        httpGet.setHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        httpGet.setHeader("sec-fetch-site", "same-site");
        httpGet.setHeader("sec-fetch-mode", "navigate");
        httpGet.setHeader("sec-fetch-user", "?1");
        httpGet.setHeader("sec-fetch-dest", "document");
        httpGet.setHeader("referer", "https://flyarystan.com/");
        httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpGet.setHeader("priority", "u=0, i");

        try {
            HttpResponse execute = client.execute(httpGet);
//            logger.debug("{}>>{}请求Response>>{}" + "1" + "搜索航班" + execute.getStatusLine().toString());
            int code = execute.getStatusLine().getStatusCode();
            String content = readHtmlContentFromEntity(execute.getEntity());
            logger.info("calendar响应:" + code + ";响应:" + (content.contains("flight-no") ? "成功" : "失败"));
//            if (code.equals("200")) {
            if (code == 200) {
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("code", code);
                jsonObject1.put("data", content);
                return jsonObject1.toString();
            } else {
                return "ERROR_calendar失败";
            }
//            ////System.out.println(code + content);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("{}>>{}请求Response>>calendar获取异常");
            return "ERROR_calendar异常";
        }
    }

    private String search(CloseableHttpClient client, String origin, String destination, String departureDate, int adults, int children, int infants, String cur) {
        String[] split = departureDate.split("-");
//        String url = "https://booking.flyarystan.com/ibe/availability?tripType=ONE_WAY&depPort=" + origin + "&arrPort=" + destination + "&departureDate=" + split[2] + "." + split[1] + "." + split[0] + "&adult=" + (adults + children + infants) + "&child=0&infant=0&currency=" + cur + "&lang=en&promotionCode=";
        String url = "https://booking.flyarystan.com/ibe/availability?tripType=ONE_WAY&depPort=ALA&arrPort=SCO&departureDate=22.08.2025&adult=1&child=0&infant=0&currency=KZT&lang=en&promotionCode=";
        HttpGet httpGet = new HttpGet(url);

//        httpGet.setHeader("Host", "booking.flyarystan.com");
//        httpGet.setHeader("Cookie", "visid_incap_1880816=ES/fc8YbSNukZy0TeCK+YEA1nGgAAAAAQUIPAAAAAADgDTmiBmt/T+Q+v/YDaBmW; nlbi_1880816=f9VeO51rEG4F6kuhxygCuQAAAAAjAKuWpYubnPWh8xzZZWr+; visid_incap_3179598=3urgTympQiWyLhMMlDfa6UE1nGgAAAAAQUIPAAAAAACRpcT5t7//dZS1OZ1KUEYa; GCLB=CLi7mLKMnMeLnAEQAw; nlbi_3179598=KYvmDXN2EgRbicL7feSt1AAAAACQIKJ0SCN0Q7wmCbnxXMcl; AMP_MKTG_d45dd76c2e=JTdCJTdE; _gcl_au=1.1.921488106.1755067791; _gid=GA1.2.218065903.1755067791; _ym_uid=1755067792299308896; _ym_d=1755067792; incap_ses_1826_1880816=ZnUyGAqUaREp7ft5sEFXGVVDnGgAAAAABMVJBDkN9dLgLLC/92nEXg==; incap_ses_1369_1880816=CCUKXgFg/U7oBUX5oKr/EsNfnGgAAAAAWiMVDHOf5mXp5NfcmiUywg==; incap_ses_1833_1880816=YekeRbnvKQhbhVn5JiBwGc9mnGgAAAAAQytJGfERaxX9b6oWFEN9ag==; incap_ses_8078_1880816=USNiGl/oelfYa2SdidIacM5tnGgAAAAAl+hrp8WroQ5J4T0NV8k88g==; incap_ses_7229_1880816=nO81XdCCLWa2ys51kpFSZJ2FnGgAAAAAKgIUtPAbW5rRDEo1kJDzmw==; incap_ses_2107_1880816=PH3za38b83NZVyu2tZE9HSKJnGgAAAAAqjxzP8WglozDEM3Zk/sj5A==; incap_ses_1002_1880816=ngGsN35kRAwEEOAaDtLnDSdHnWgAAAAAxbCyuVToynVf7NBhfpnukw==; incap_ses_228_1880816=Lf+dNJBU/moKumzsNQUqA5VOnWgAAAAArkO/roaRg2ihbMswb+58Vw==; incap_ses_1448_3179598=EEPUOlqUNwdJf/06nFQYFJZOnWgAAAAA/+oayIQv0qQMwItiXzTw9Q==; _ym_isad=2; _ym_visorc=w; _fbp=fb.1.1755139902019.573578595223696519; nlbi_1880816_2147483392=NVL+RyKtoyk7Q1/yxygCuQAAAACI/V2xX/GE0mBWUl3wg7GD; cookieyes-consent=consentid:QzRYbzR1c1JEUEVTOVNOcDJocnZ5UlcyUWdvcVpKTko,consent:yes,action:no,necessary:yes,functional:yes,analytics:yes,performance:yes,advertisement:yes; _ga=GA1.2.1867591194.1755067791; _ga_75PYZFVBX6=GS2.1.s1755139883" + System.getenv("o2") + System.getenv("g1") + System.getenv("t1755139948") + System.getenv("j60") + System.getenv("l0") + System.getenv("h0") + "; _ga_FYLXGW40XL=GS2.1.s1755139883" + System.getenv("o2") + System.getenv("g1") + System.getenv("t1755140004") + System.getenv("j4") + System.getenv("l0") + System.getenv("h0") + "; ARRAffinity=fbb4eba32680e6cf5d183f1c3ecb0c33fb696cefc25dc7b1e847d702ee0bd87c; ARRAffinitySameSite=fbb4eba32680e6cf5d183f1c3ecb0c33fb696cefc25dc7b1e847d702ee0bd87c; AMP_d45dd76c2e=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI1YTU5MWFkZC1kYmQ4LTQ2ZmYtOWVmOS1hZThiNjYwMmUyYWMlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzU1MTM5ODk4NjE3JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJsYXN0RXZlbnRUaW1lJTIyJTNBMTc1NTE0MDAzNjExMiUyQyUyMmxhc3RFdmVudElkJTIyJTNBMTglN0Q=");
//        httpGet.setHeader("sec-ch-ua", "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"");
//        httpGet.setHeader("sec-ch-ua-mobile", "?0");
//        httpGet.setHeader("sec-ch-ua-platform", "\"macOS\"");
//        httpGet.setHeader("upgrade-insecure-requests", "1");
//        httpGet.setHeader("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//        httpGet.setHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
//        httpGet.setHeader("sec-fetch-site", "same-site");
//        httpGet.setHeader("sec-fetch-mode", "navigate");
//        httpGet.setHeader("sec-fetch-user", "?1");
//        httpGet.setHeader("sec-fetch-dest", "document");
//        httpGet.setHeader("referer", "https://flyarystan.com/");
//        httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
//        httpGet.setHeader("priority", "u=0, i");


        httpGet.setHeader("Host", "booking.flyarystan.com");
//        httpGet.setHeader("Cookie", "JSESSIONID=6866ABD8B272E91D2B061ABDF43AA77A; visid_incap_1880816=ES/fc8YbSNukZy0TeCK+YEA1nGgAAAAAQUIPAAAAAADgDTmiBmt/T+Q+v/YDaBmW; nlbi_1880816=f9VeO51rEG4F6kuhxygCuQAAAAAjAKuWpYubnPWh8xzZZWr+; visid_incap_3179598=3urgTympQiWyLhMMlDfa6UE1nGgAAAAAQUIPAAAAAACRpcT5t7//dZS1OZ1KUEYa; GCLB=CLi7mLKMnMeLnAEQAw; nlbi_3179598=KYvmDXN2EgRbicL7feSt1AAAAACQIKJ0SCN0Q7wmCbnxXMcl; AMP_MKTG_d45dd76c2e=JTdCJTdE; _gcl_au=1.1.921488106.1755067791; _ym_uid=1755067792299308896; _ym_d=1755067792; incap_ses_1826_1880816=ZnUyGAqUaREp7ft5sEFXGVVDnGgAAAAABMVJBDkN9dLgLLC/92nEXg==; incap_ses_1369_1880816=CCUKXgFg/U7oBUX5oKr/EsNfnGgAAAAAWiMVDHOf5mXp5NfcmiUywg==; incap_ses_1833_1880816=YekeRbnvKQhbhVn5JiBwGc9mnGgAAAAAQytJGfERaxX9b6oWFEN9ag==; incap_ses_7229_1880816=nO81XdCCLWa2ys51kpFSZJ2FnGgAAAAAKgIUtPAbW5rRDEo1kJDzmw==; incap_ses_2107_1880816=PH3za38b83NZVyu2tZE9HSKJnGgAAAAAqjxzP8WglozDEM3Zk/sj5A==; incap_ses_1002_1880816=ngGsN35kRAwEEOAaDtLnDSdHnWgAAAAAxbCyuVToynVf7NBhfpnukw==; _ym_isad=2; _fbp=fb.1.1755139902019.573578595223696519; cookieyes-consent=consentid:QzRYbzR1c1JEUEVTOVNOcDJocnZ5UlcyUWdvcVpKTko,consent:yes,action:no,necessary:yes,functional:yes,analytics:yes,performance:yes,advertisement:yes; ckIBEpersist=!02ihwQz3wvh80+NslCzSo16hMMp6am09uXotSlEKpc+NksY5dFco/u5OJD0kY2iv756kxJxXvMkl9g==; nlbi_3179598_2147483392=r2JaLRZ98AxysxcefeSt1AAAAACcMt3h3f33Hp1TB9SXyDDr; reese84=3:kBRozrPb2ez8OEU/At8mkw==:aaksCTwhNHa5qBbr+3wYqGdQjs+YOsD8gWgbr/a1jTMuGdz3OBqKpX1B/S797TEcA74+GIQa3PXRvOf/qzzxAY+8AQ/+TWA+xK1Jrq0eKT+MOtxco7m4GTQ47ZNiWZ1fNymb8zE2HWHM6+bBqfCTkvzp8G75JkLUDw0/4gKx2CVCQHRuQVXyscpQ/cX2JAU08R7za7EUz+J1i4c4sf5lgQh8WJCw4Um/DdHXfr6AL2WdSEcO9XmMykSjs8/EMSE8Pb/DtLIyjA4Fk6CBlvMISqyog/R3yJo4xY7wV+iiHWDD1knPgQCNTLtwibc6SH4ixIiJSsyjFtl/O8rSEFv2KOaPAKH222TjUOqqs8zTmtSbjj/9UyKvzU7P2UG2UHa632d49SL4V9rE6gzDHXnrgvwMqNIEj833XiskTF4Pkl0eSPpG4Zw+UM4ObeOsXlA02WnVU1rDqduAaKQBGA3eng==:WSLiI6OVDHs7u22CKS10Zly6GkUrWQ0uCsXQMaKzNjM=; incap_ses_8078_1880816=5DVnEAcAu1eySf2didIacMuBnWgAAAAAY3TeqwiO4MVw7ZfYVvY68Q==; ARRAffinitySameSite=0bc99887f10809c8a4fbce625f6790c7807de861f12abc25e7ad210960a9aa25; incap_ses_228_1880816=OnIuXZYyyxc4qc3sNQUqA7iCnWgAAAAApxNI2dzalPIBJF/tXgC8YQ==; incap_ses_1448_3179598=1AdSR5Q3uk0FSyY7nFQYFFKFnWgAAAAAE6MCfsI9WVzwQSqJ37hU5w==; nlbi_1880816_2147483392=71hzaQlCk1QjMRbrxygCuQAAAACTjvetbNjHN8/6+PcJ/jJj; _gid=GA1.2.1027239725.1755153750; _ga_75PYZFVBX6=GS2.1.s1755153748" + System.getenv("o1") + System.getenv("g0") + System.getenv("t1755153748") + System.getenv("j60") + System.getenv("l0") + System.getenv("h0") + "; _ga=GA1.1.980050143.1755153750; _ga_FYLXGW40XL=GS2.1.s1755153748" + System.getenv("o1") + System.getenv("g0") + System.getenv("t1755153748") + System.getenv("j60") + System.getenv("l0") + System.getenv("h0") + "; _ym_visorc=w; ARRAffinity=0bc99887f10809c8a4fbce625f6790c7807de861f12abc25e7ad210960a9aa25; AMP_d45dd76c2e=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjJkYjdmZTkzMC1mZGQ2LTRjNjAtYmYzMS1jODkwMmMyZGFjNWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzU1MTUzNzQ4NDE3JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJsYXN0RXZlbnRUaW1lJTIyJTNBMTc1NTE1MzgyODQyNCUyQyUyMmxhc3RFdmVudElkJTIyJTNBNiU3RA==")
        httpGet.setHeader("sec-ch-ua", "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"");
        httpGet.setHeader("sec-ch-ua-mobile", "?0");
        httpGet.setHeader("sec-ch-ua-platform", "\"macOS\"");
        httpGet.setHeader("upgrade-insecure-requests", "1");
        httpGet.setHeader("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        httpGet.setHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        httpGet.setHeader("sec-fetch-site", "same-site");
        httpGet.setHeader("sec-fetch-mode", "navigate");
        httpGet.setHeader("sec-fetch-user", "?1");
        httpGet.setHeader("sec-fetch-dest", "document");
        httpGet.setHeader("referer", "https://flyarystan.com/");
        httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpGet.setHeader("priority", "u=0, i");

        try {
            HttpResponse execute = client.execute(httpGet);
//            logger.debug("{}>>{}请求Response>>{}" + "1" + "搜索航班" + execute.getStatusLine().toString());
            int code = execute.getStatusLine().getStatusCode();
            String content = readHtmlContentFromEntity(execute.getEntity());
            logger.info("搜索响应:" + code + ";响应:" + (content.contains("flight-no") ? "成功" : "失败"));
//            if (code.equals("200")) {
            if (code == 200) {
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("code", code);
                jsonObject1.put("data", content);
                return jsonObject1.toString();
            } else {
                return "ERROR_搜索航班失败";
            }
//            ////System.out.println(code + content);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("{}>>{}请求Response>>官网id获取异常");
            return "ERROR_搜索航班异常";
        }
    }

    public void runFlight() {
        DefaultHttpClient client = VjHttpUtil.getHttpClient("");
        String[] flights = {"ALA", "SCO", "AKX", "GUW", "NQZ", "KGF", "KSN", "KZO", "PWQ", "PLX", "HSA", "URA", "UKK", "CIT", "IST", "KUT", "IKU", "BSZ", "GYD", "TAS", "URC", "YIN"};
        for (String flight : flights) {
            String list = getFlight(client, flight);
            JSONArray array = JSONArray.parseArray(list);
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                for (String s : jsonObject.keySet()) {
                    JSONArray jsonArray = jsonObject.getJSONArray(s);
                    for (int j = 0; j < jsonArray.size(); j++) {
                        JSONObject jsonObject1 = jsonArray.getJSONObject(j);
                        String city = jsonObject1.getString("city");
                        String code = jsonObject1.getString("code");
                        String country = jsonObject1.getString("country");
                        if (city.equals("FRU")) {
                            continue;
                        }
                        System.out.println("出发:" + flight + ",到达城市:" + city + ",code:" + code + ",国家:" + country);
                    }
                }
            }

        }
    }

    public String getFlight(DefaultHttpClient client, String dep) {
        String url = "https://kzr-ports.hosting.aero/ibe//search/portGroupsByCountry/" + dep;
        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("Host", "kzr-ports.hosting.aero");
        httpGet.setHeader("sec-ch-ua-platform", "\"macOS\"");
        httpGet.setHeader("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        httpGet.setHeader("sec-ch-ua", "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"");
        httpGet.setHeader("sec-ch-ua-mobile", "?0");
        httpGet.setHeader("accept", "*/*");
        httpGet.setHeader("origin", "https://flyarystan.com");
        httpGet.setHeader("sec-fetch-site", "cross-site");
        httpGet.setHeader("sec-fetch-mode", "cors");
        httpGet.setHeader("sec-fetch-dest", "empty");
        httpGet.setHeader("referer", "https://flyarystan.com/");
        httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpGet.setHeader("priority", "u=1, i");

        try {
            HttpResponse execute = client.execute(httpGet);
//            logger.debug("{}>>{}请求Response>>{}" + "1" + "搜索航班" + execute.getStatusLine().toString());
            int code = execute.getStatusLine().getStatusCode();
            String content = readHtmlContentFromEntity(execute.getEntity());
//            logger.info("搜索响应:" + code + ";响应:" + (content.contains("flight-no") ? "成功" : "失败"));
//            if (code.equals("200")) {
            if (code == 200) {
                return content;
            } else {
                return "ERROR_搜索航班失败";
            }
//            ////System.out.println(code + content);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("{}>>{}请求Response>>官网id获取异常");
            return "ERROR_搜索航班异常";
        }
    }
}
