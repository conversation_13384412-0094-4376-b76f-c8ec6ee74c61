package com.llq.hs.service.crawler.config.thread;

import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Component
public class ThreadPoolMonitor {

    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolMonitor.class);

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Scheduled(fixedRate = 30000) // 每30秒监控一次
    public void monitor() {
        ThreadPoolExecutor pool = taskExecutor.getThreadPoolExecutor();
        logger.info("线程池监控 - 活跃线程: {}/{}, 队列: {}/{}, 完成任务: {}",
                pool.getActiveCount(),
                pool.getPoolSize(),
                pool.getQueue().size(),
                pool.getQueue().remainingCapacity() + pool.getQueue().size(),
                pool.getCompletedTaskCount());
    }
}