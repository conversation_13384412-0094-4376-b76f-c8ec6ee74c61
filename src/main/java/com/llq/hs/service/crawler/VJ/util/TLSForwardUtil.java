package com.llq.hs.service.crawler.VJ.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

import static com.llq.hs.service.crawler.VJ.http.VjHttpUtil.getHttpClient;
import static com.llq.hs.service.crawler.VJ.http.VjHttpUtil.readHtmlContentFromEntity;


public class TLSForwardUtil {
    private static final Logger logger = LoggerFactory.getLogger(TLSForwardUtil.class);


    public static void main(String[] args) {
        TLSForwardUtil TLSForwardUtil = new TLSForwardUtil();
        TLSForwardUtil.postTlsDome();
//        Map<String, String> akamai = TLSForwardUtil.getAkamai("https://www.vueling.com/en", "https://www.vueling.com/A_djaH114/KdfM/zVR/1SgPC4H_sDU/NYE1pLzkNGYp9D/UR4dAQ/bRQbSDt/qInI","127.0.0.1:8888",481);
//        String abck = akamai.get("_abck");
//        String bm_sz = akamai.get("bm_sz");
//        String ua = akamai.get("ua");
//        System.out.println(abck);
//        System.out.println(bm_sz);
//        System.out.println(ua);
    }



    //post类型的TLS请求用例
    public void postTlsDome() {

        String a = "{\"url\":\"https://soar.cebupacificair.com/ceb-omnix_proxy\",\"proxy\":\"http://lum-brd-customer-hl_19cb0fe8-zone-ba-country-US-session-61834380:<EMAIL>:22225\",\"headers\":{\"Origin\":\"https://www.cebupacificair.com\",\"sec-fetch-mode\":\"cors\",\"sec-fetch-site\":\"same-site\",\"accept-language\":\"zh-Hans-CN;q=1.0\",\"cookie\":\"dtCookie=v_4_srv_3_sn_02b1a17a0b6f433f815c3c8ba9908ecc_perc_100000_ol_0_mul_1_app-3Ab471fd2b229e5313_0_app-3Aeed5f1a6be5f3e72_1_app-3Aabf5cd7d6840a226_0_rcs-3Acss_0\",\"Referer\":\"https://www.cebupacificair.com\",\"User-Agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"Accept-Encoding\":\"gzip, deflate, br\",\"x-path\":\"U2FsdGVkX19zeTYbCAEuymYJ1tJb7ljSCFMUL6ixnlnEoOkLCeJybCIn9VJLb/xXuutdx989gfGGDdYrtLcxiw==\",\"content\":\"U2FsdGVkX182Or/ZhUHKZEf+Dx73wtvDVECB0kvo667c4c2cabE273AI2OUSCDxNVePh0wcCPsaAGz909f7208b6U2FsdGVkX19KdAUUs62SNwwmGe6u0YSbe06fUGZ4tsRuA4vb9YdcQtFm1ilkLIXAXdqFVn9ddT8zWUOagEfJtAVPulnbCU8/Xa2yJU7YGpa3gLeHeww0t8JWHigq5c+GwdnQNn7sCKosW5z9Vgx0+cjyUyam7JypuqQAMMM1KXjY6fRxuoI5d8lhJRn/tx+Ut4ZqhII5NVCSyv/w/Dt8PaD8GUv1cucsREzPq8EE3Ahfe7aOlFeowHIZM+jZkhTpCUEon946BcQ+ETMx3xanOhdCWBPRdyacJNTo5GzDass=4RsdQ8kBwyR8TRWxFaHZgn1PGF7y4h5k8fWbukCoDZ8kRg==\",\"accept\":\"*/*\",\"Authorization\":\"Bearer 667c4c2cabE273AI2OUSCDxNVePh0wcCPsaAGz\",\"X-Auth-Token\":\"909f7208b6U2FsdGVkX19KdAUUs62SNwwmGe6u0YSbe06fUGZ4tsRuA4vb9YdcQtFm1ilkLIXAXdqFVn9ddT8zWUOagEfJtAVPulnbCU8/Xa2yJU7YGpa3gLeHeww0t8JWHigq5c+GwdnQNn7sCKosW5z9Vgx0+cjyUyam7JypuqQAMMM1KXjY6fRxuoI5d8lhJRn/tx+Ut4ZqhII5NVCSyv/w/Dt8PaD8GUv1cucsREzPq8EE3Ahfe7aOlFeowHIZM+jZkhTpCUEon946BcQ+ETMx3xanOhdCWBPRdyacJNTo5GzDass=\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"scope\":\"omnix\",\"host\":\"soar.cebupacificair.com\",\"content-type\":\"application/json\",\"sec-fetch-dest\":\"empty\"},\"method\":\"get\",\"types\":\"curl\",\"timeout\":60,\"redirect\":true,\"random_tls_extension_order\":true}";
        JSONObject sendInfo = JSONObject.parseObject(a);
        TLSForwardUtil TLSForwardUtil = new TLSForwardUtil();

        Map<String, String> map = sendTls(sendInfo, "127.0.0.1:8888");
        String code = map.get("code");
        String content = map.get("content");
        System.out.println("请求状态码:" + code);
        System.out.println("请求参数:" + content);
    }


    public void getTlsDome() {
        JSONObject sendInfo = new JSONObject();
        //目标url
        sendInfo.put("url", "https://akm.sakura-luo.top/84_ck?token=test&version=https%3A%2F%2Fbeta.makeabooking.flyscoot.com%2FTwo-discharted-Not-ere-int-when-is-are-Poss-A-Sa%3Fd%3Dbeta.makeabooking.flyscoot.com&ua=");
        //代理
        sendInfo.put("proxy", "http://hourse-res-us:<EMAIL>:5959");
        //目标网站请求头
        JSONObject headers = new JSONObject();
        headers.put("Host", "akm.sakura-luo.top");
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("Accept", "*/*");
        sendInfo.put("headers", headers);
        //请求方式(默认get)
        sendInfo.put("method", "get");
        //选择发包器(默认curl)
        sendInfo.put("types", "curl");
        //目标网站cookie
        JSONObject cookies = new JSONObject();
        cookies.put("key", "value");
        sendInfo.put("cookies", cookies);
        //超时时间（默认30s）
        sendInfo.put("timeout", 10);
        //是否重定向（默认是）
        sendInfo.put("redirect", true);

        TLSForwardUtil TLSForwardUtil = new TLSForwardUtil();

        Map<String, String> map = sendTls(sendInfo, "127.0.0.1:8888");
        String code = map.get("code");
        String content = map.get("content");
        System.out.println("请求状态码:" + code);
        System.out.println("请求参数:" + content);
    }


    //获取Akamai函数 获取akamai参数包含数字为-0的
    //requests_url 获取akamai参数的官网
    //js_url 获取akamai参数的请求
    //proxy 本地请求是需要挂的代理
    public Map<String, String> getAkamai(String requests_url, String js_url,String proxy) {
        DefaultHttpClient client = getHttpClient("");

        JSONObject sendInfo = new JSONObject();
        sendInfo.put("url", js_url);
        JSONObject headers = new JSONObject();
        headers.put("Connection", "close");
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        sendInfo.put("headers", headers);

        Map<String, String> map = TLSForwardUtil.sendTls(sendInfo, proxy);
        String content = map.get("content");
        JSONObject contentJSON = JSONObject.parseObject(content);
        JSONObject cookies = contentJSON.getJSONObject("cookies");
        String _abck = cookies.getString("_abck");
        String bm_sz = cookies.getString("bm_sz");
        String flag = "";
        for (int i = 0; i < 5; i++) {
            if (!flag.equals("0")) {
                CloseableHttpResponse response2 = null;
                try {
                    //发送第二次请求
                    HttpPost httpPost = new HttpPost("http://172.233.75.130:5110/akm_data");
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("requests_url", requests_url);
                    jsonObject.put("_abck", _abck);
                    jsonObject.put("bm_sz", bm_sz);
                    jsonObject.put("ua", "");
                    httpPost.setHeader("Content-Type", "application/json");
                    httpPost.setEntity(new StringEntity(jsonObject.toString(), "UTF-8"));
                    HttpResponse response1 = client.execute(httpPost);
                    String akamaiData = readHtmlContentFromEntity(response1.getEntity());
                    JSONObject data = JSONObject.parseObject(akamaiData);
                    String sensorData = data.getString("sensor_data");

                    String ua = data.getString("ua");
//                    logger.info("破盾获取abck&bmsz");

                    //第三次请求
                    JSONObject sendData = new JSONObject();
                    sendData.put("url",js_url);
                    sendData.put("method","post");


                    headers.put("User-Agent",ua);
                    sendData.put("headers",headers);

                    JSONObject cookies1 = new JSONObject();
                    cookies1.put("_abck",_abck);
                    cookies1.put("bm_sz",bm_sz);
                    sendData.put("cookies",cookies1);

                    JSONObject json = new JSONObject();
                    json.put("sensor_data", sensorData);
                    sendData.put("json",json);

                    Map<String, String> map1 = sendTls(sendData, proxy);
                    String content1 = map1.get("content");
                    JSONObject contentJSON1 = JSONObject.parseObject(content1);
                    JSONObject cookies2 = contentJSON1.getJSONObject("cookies");
                    _abck = cookies2.getString("_abck");
                    int indexOf = _abck.indexOf("~");
                    flag = _abck.substring(indexOf + 1, indexOf + 3);
                    if (flag.equals("-1")) {
                        logger.info("获取akamai为-1");
                        continue;
                    }
                    HashMap<String, String> abckMap = new HashMap<>();
                    abckMap.put("_abck",_abck);
                    abckMap.put("bm_sz",bm_sz);
                    abckMap.put("ua",ua);
                    return abckMap;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    //获取Akamai函数 获取akamai参数长度大于ck_len
    //requests_url 获取akamai参数的官网
    //js_url 获取akamai参数的请求
    //proxy 本地请求是需要挂的代理
    //ck_len akamai参数设置的长度
    public Map<String, String> getAkamai(String requests_url, String js_url,String proxy, int ck_len) {
        DefaultHttpClient client = getHttpClient(proxy);

        JSONObject sendInfo = new JSONObject();
        sendInfo.put("url", js_url);
        JSONObject headers = new JSONObject();
        headers.put("Connection", "close");
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        sendInfo.put("headers", headers);

        Map<String, String> map = TLSForwardUtil.sendTls(sendInfo, proxy);
        String content = map.get("content");
        JSONObject contentJSON = JSONObject.parseObject(content);
        JSONObject cookies = contentJSON.getJSONObject("cookies");
        String _abck = cookies.getString("_abck");
        String bm_sz = cookies.getString("bm_sz");
        String flag = "";
        for (int i = 0; i < 5; i++) {
            if (_abck.length() < ck_len) {
                CloseableHttpResponse response2 = null;
                try {
                    //发送第二次请求
                    HttpPost httpPost = new HttpPost("http://172.233.75.130:5110/akm_data");
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("requests_url", requests_url);
                    jsonObject.put("_abck", _abck);
                    jsonObject.put("bm_sz", bm_sz);
                    jsonObject.put("ua", "");
                    httpPost.setHeader("Content-Type", "application/json");
                    httpPost.setEntity(new StringEntity(jsonObject.toString(), "UTF-8"));
                    HttpResponse response1 = client.execute(httpPost);
                    String akamaiData = readHtmlContentFromEntity(response1.getEntity());
                    JSONObject data = JSONObject.parseObject(akamaiData);
                    String sensorData = data.getString("sensor_data");

                    String ua = data.getString("ua");
//                    logger.info("破盾获取abck&bmsz");

                    //第三次请求
                    JSONObject sendData = new JSONObject();
                    sendData.put("url",js_url);
                    sendData.put("method","post");


                    headers.put("User-Agent",ua);
                    sendData.put("headers",headers);

                    JSONObject cookies1 = new JSONObject();
                    cookies1.put("_abck",_abck);
                    cookies1.put("bm_sz",bm_sz);
                    sendData.put("cookies",cookies1);

                    JSONObject json = new JSONObject();
                    json.put("sensor_data", sensorData);
                    sendData.put("json",json);

                    Map<String, String> map1 = sendTls(sendData, proxy);
                    String content1 = map1.get("content");
                    JSONObject contentJSON1 = JSONObject.parseObject(content1);
                    JSONObject cookies2 = contentJSON1.getJSONObject("cookies");
                    _abck = cookies2.getString("_abck");
                    if (_abck.length() < ck_len) {
                        logger.info("akamai长度小于"+ck_len);
                        continue;
                    }
                    HashMap<String, String> abckMap = new HashMap<>();
                    abckMap.put("_abck",_abck);
                    abckMap.put("bm_sz",bm_sz);
                    abckMap.put("ua",ua);
                    return abckMap;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    //tls请求函数
    //jsonObject 请求tls的参数json
    //proxy 本地请求是需要挂的代理
    public static Map<String, String> sendTls(JSONObject jsonObject, String proxy) {


//        proxy = "127.0.0.1:8888";
        DefaultHttpClient httpClient = getHttpClient(proxy);


        JSONObject data = new JSONObject();
        data.put("url", jsonObject.getString("url"));
        data.put("headers", jsonObject.getJSONObject("headers"));

        if (jsonObject.containsKey("proxy_str")) {
            data.put("proxy_str", jsonObject.getString("proxy_str"));
        }
        if (jsonObject.containsKey("method")) {
            data.put("method", jsonObject.getString("method"));
        } else {
            data.put("method", "get");
        }

        if (jsonObject.containsKey("tls_type")) {
            data.put("tls_type", jsonObject.getString("tls_type"));
        } else {
            data.put("tls_type", "curl");
        }

        if (jsonObject.containsKey("cookies")) {
            data.put("cookies", jsonObject.getJSONObject("cookies"));
        }
        if (jsonObject.containsKey("timeout")) {
            data.put("timeout", jsonObject.getDouble("timeout"));
        }

//        if (jsonObject.containsKey("json")) {
//            data.put("json", jsonObject.getString("json"));
//        }

        if (jsonObject.containsKey("data")) {
            data.put("data", jsonObject.getJSONObject("data"));
        }

//        if (jsonObject.containsKey("timeout")) {
//            data.put("timeout", jsonObject.getInt("timeout"));
//        } else {
//            data.put("timeout", 30);
//        }

//        if (jsonObject.containsKey("redirect")) {
//            data.put("redirect", jsonObject.getBoolean("redirect"));
//        } else {
//            data.put("redirect", true);
//        }

//        if (jsonObject.containsKey("random_tls_extension_order")) {
//            data.put("random_tls_extension_order", jsonObject.getBoolean("random_tls_extension_order"));
//        }

        String tls_url = "http://152.136.206.189:8866/proxy";
//        String tls_url = "http://192.168.124.220:8000/api/v1/request";

//        String url = "";
//        if(!jsonObject.containsKey("tls_url")) {
//            url = tls_url;
//        } else {
//            url = tls_url;
//        }
        HttpPost httpPost = new HttpPost(tls_url);
        httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
        String content = "";
        String code = "";
        try {
            httpPost.setEntity(new StringEntity(data.toString()));
            CloseableHttpResponse execute = httpClient.execute(httpPost);
            code = execute.getStatusLine().getStatusCode() + "";
//            content = readHtmlContentFromEntity(execute.getEntity());
            content = EntityUtils.toString(execute.getEntity(), "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
            Map<String, String> map = new HashMap<>();
            map.put("code", 300+"");
            map.put("content", "ERROR_请求异常了_"+e.getMessage());
            return map;

        }
        Map<String, String> map = new HashMap<>();
        map.put("code", code);
        map.put("content", content);

        return map;
    }
}
