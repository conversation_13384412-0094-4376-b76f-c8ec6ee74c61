package com.llq.hs.service.crawler.VJ.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.llq.hs.service.crawler.VJ.entity.LccMailEntity;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【lcc_mail】的数据库操作Service
* @createDate 2025-07-12 14:42:03
*/
public interface LccMailEntityService extends IService<LccMailEntity> {

    Date selectMaxReceiveDate(String code);
}
