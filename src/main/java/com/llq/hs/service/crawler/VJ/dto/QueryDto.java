package com.llq.hs.service.crawler.VJ.dto;

import com.google.common.base.Strings;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025/07/22
 **/
@Data
public class QueryDto {

    private  String depCity;
    private  String arrCity;
    private  String departureDate;
    private  String currency;
    private  String depAirport;
    private  String arrAirport;
    private String id;// 用于定位的唯一id
    private Integer adultNumber;
    private Integer childNumber;
    private Integer infantNumber;

    /**
     * 参数是否不为空
     * @return
     */
    public boolean valid(){
        return !Strings.isNullOrEmpty(depCity)
                && !Strings.isNullOrEmpty(arrCity)
                && !Strings.isNullOrEmpty(departureDate)
                && !Strings.isNullOrEmpty(currency)
                && !Strings.isNullOrEmpty(depAirport)
                && !Strings.isNullOrEmpty(arrAirport);
    }


}
