package com.llq.hs.service.crawler.mq.req;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 接收直连系统消息队列请求对象实体类
 * <AUTHOR>
 */
@Data
public class SaasSysRequestMessage implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    // 航司
    private String hangSi;
    //出发城市三字码
    private String depCity;
    //到达城市三字码
    private String arrCity;
    //出发日期
    private String departureDate;
    //货币类型
    private String currency;
    // 出发机场三字码
    private String depAirport;
    //到达机场三字码
    private String arrAirport;
}
