package com.llq.hs.service.crawler.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;


/**
 * <AUTHOR>
 * @description: 完全异步HTTP请求工具类
 * 特点：
 * - 完全非阻塞的异步处理
 * - 支持响应式编程范式
 * - 支持自定义成功/失败处理器
 * - 内置请求统计和监控
 * - 支持请求重试机制
 * 适合场景：
 * 高并发场景：-需要处理大量并发请求
 * -服务间异步通信
 * 实时数据处理：-需要快速响应的场景
 * 事件驱动：-基于回调的事件处理
 * @date 2025/11/5
 */
@Component
public class FullyAsyncHttpClient {

    private static final Logger logger = LoggerFactory.getLogger(FullyAsyncHttpClient.class);

    private final WebClient webClient;
    private final AtomicLong requestCounter = new AtomicLong(0);
    private final Map<String, RequestStats> requestStats = new ConcurrentHashMap<>();
    //默认超时-秒
    private static final int TIMEOUT=30;

    // 请求统计信息
    private static class RequestStats {
        long totalRequests;
        long successRequests;
        long failedRequests;
        long totalResponseTime;
    }

    // 请求配置
    public static class RequestConfig {
        private Duration timeout = Duration.ofSeconds(TIMEOUT);
        private int maxRetries = 0;
        private Map<String, String> headers;
        private Map<String, String> queryParams;

        public static RequestConfig builder() {
            return new RequestConfig();
        }

        public RequestConfig timeout(Duration timeout) {
            this.timeout = timeout;
            return this;
        }

        public RequestConfig maxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }

        public RequestConfig headers(Map<String, String> headers) {
            this.headers = headers;
            return this;
        }

        public RequestConfig queryParams(Map<String, String> queryParams) {
            this.queryParams = queryParams;
            return this;
        }

        public RequestConfig build() {
            return this;
        }

        public Duration getTimeout() { return timeout; }
        public int getMaxRetries() { return maxRetries; }
        public Map<String, String> getHeaders() { return headers; }
        public Map<String, String> getQueryParams() { return queryParams; }
    }

    // 响应结果
    public static class HttpResponse {
        private final String requestId;
        private final boolean success;
        private final int statusCode;
        private final String body;
        private final String errorMessage;
        private final long responseTime;
        private final Map<String, String> headers;

        public HttpResponse(String requestId, boolean success, int statusCode, String body,
                            String errorMessage, long responseTime, Map<String, String> headers) {
            this.requestId = requestId;
            this.success = success;
            this.statusCode = statusCode;
            this.body = body;
            this.errorMessage = errorMessage;
            this.responseTime = responseTime;
            this.headers = headers;
        }

        public String getRequestId() { return requestId; }
        public boolean isSuccess() { return success; }
        public int getStatusCode() { return statusCode; }
        public String getBody() { return body; }
        public String getErrorMessage() { return errorMessage; }
        public long getResponseTime() { return responseTime; }
        public Map<String, String> getHeaders() { return headers; }

        @Override
        public String toString() {
            return String.format("HttpResponse{requestId='%s', success=%s, statusCode=%d, responseTime=%dms}",
                    requestId, success, statusCode, responseTime);
        }
    }

    // 回调接口
    public interface ResponseCallback {
        void onSuccess(HttpResponse response);
        void onError(HttpResponse response);
    }

    public FullyAsyncHttpClient() {
        this.webClient = WebClient.builder()
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.USER_AGENT, "Fully-Async-HTTP-Client/1.0")
                .build();
    }

    public FullyAsyncHttpClient(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.build();
    }

    /**
     * 完全异步GET请求 - 使用回调
     */
    public void getAsync(String url, RequestConfig config, ResponseCallback callback) {
        executeRequest(HttpMethod.GET.name(), url, null, config, callback);
    }

    /**
     * 完全异步POST请求 - 使用回调
     */
    public void postAsync(String url, Object body, RequestConfig config, ResponseCallback callback) {
        executeRequest(HttpMethod.POST.name(), url, body, config, callback);
    }

    /**
     * 完全异步PUT请求 - 使用回调
     */
    public void putAsync(String url, Object body, RequestConfig config, ResponseCallback callback) {
        executeRequest(HttpMethod.PUT.name(), url, body, config, callback);
    }

    /**
     * 完全异步DELETE请求 - 使用回调
     */
    public void deleteAsync(String url, RequestConfig config, ResponseCallback callback) {
        executeRequest(HttpMethod.DELETE.name(), url, null, config, callback);
    }

    /**
     * 返回Mono的完全异步GET请求 - 响应式编程
     */
    public Mono<HttpResponse> getAsync(String url, RequestConfig config) {
        return executeRequestReactive(HttpMethod.GET.name(), url, null, config);
    }

    /**
     * 返回Mono的完全异步POST请求 - 响应式编程
     */
    public Mono<HttpResponse> postAsync(String url, Object body, RequestConfig config) {
        return executeRequestReactive(HttpMethod.POST.name(), url, body, config);
    }

    /**
     * 返回Mono的完全异步PUT请求 - 响应式编程
     */
    public Mono<HttpResponse> putAsync(String url, Object body, RequestConfig config) {
        return executeRequestReactive(HttpMethod.PUT.name(), url, body, config);
    }

    /**
     * 返回Mono的完全异步DELETE请求 - 响应式编程
     */
    public Mono<HttpResponse> deleteAsync(String url, RequestConfig config) {
        return executeRequestReactive(HttpMethod.DELETE.name(), url, null, config);
    }

    /**
     * 返回CompletableFuture的异步请求 - 兼容传统异步编程
     */
    public CompletableFuture<HttpResponse> getAsyncFuture(String url, RequestConfig config) {
        return executeRequestReactive(HttpMethod.GET.name(), url, null, config).toFuture();
    }

    public CompletableFuture<HttpResponse> postAsyncFuture(String url, Object body, RequestConfig config) {
        return executeRequestReactive(HttpMethod.POST.name(), url, body, config).toFuture();
    }

    public CompletableFuture<HttpResponse> putAsyncFuture(String url, Object body, RequestConfig config) {
        return executeRequestReactive(HttpMethod.PUT.name(), url, body, config).toFuture();
    }

    public CompletableFuture<HttpResponse> deleteAsyncFuture(String url, RequestConfig config) {
        return executeRequestReactive(HttpMethod.DELETE.name(), url, null, config).toFuture();
    }

    // 核心请求执行方法 - 回调方式
    private void executeRequest(String method, String url, Object body,
                                RequestConfig config, ResponseCallback callback) {
        String requestId = generateRequestId(method, url);
        long startTime = System.currentTimeMillis();

        logger.info(" [{}] 开始完全异步{}请求: {}", requestId, method, url);

        Mono<HttpResponse> requestMono = createRequest(method, url, body, config, requestId, startTime);

        requestMono.subscribe(
                response -> {
                    if (response.isSuccess()) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError(response);
                    }
                },
                error -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    HttpResponse errorResponse = handleError(requestId, error, responseTime);
                    recordRequestStats(false, responseTime);
                    callback.onError(errorResponse);
                }
        );
    }

    // 核心请求执行方法 - 响应式方式
    private Mono<HttpResponse> executeRequestReactive(String method, String url, Object body, RequestConfig config) {
        String requestId = generateRequestId(method, url);
        long startTime = System.currentTimeMillis();

        logger.info(" [{}] 开始响应式{}请求: {}", requestId, method, url);

        return createRequest(method, url, body, config, requestId, startTime)
                .doOnError(error -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    recordRequestStats(false, responseTime);
                    logger.error("[{}] 请求执行异常: {}", requestId, error.getMessage());
                });
    }

    // 创建WebClient请求
    private Mono<HttpResponse> createRequest(String method, String url, Object body,
                                             RequestConfig config, String requestId, long startTime) {
        URI uri = buildUri(url, config);

        WebClient.RequestBodySpec requestSpec = webClient.method(org.springframework.http.HttpMethod.valueOf(method))
                .uri(uri)
                .headers(headers -> addCustomHeaders(headers, config));

        if (body != null && !method.equals(HttpMethod.GET.name()) && !method.equals(HttpMethod.DELETE.name())) {
            requestSpec.bodyValue(body);
        }

        return requestSpec.retrieve()
                .toEntity(String.class)
                .timeout(config.getTimeout())
                .map(responseEntity -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    HttpResponse response = new HttpResponse(
                            requestId, true, responseEntity.getStatusCodeValue(),
                            responseEntity.getBody(), null, responseTime,
                            extractHeaders(responseEntity.getHeaders())
                    );
                    recordRequestStats(true, responseTime);
                    logger.info("[{}] {}请求成功, 状态码: {}, 耗时: {}ms",
                            requestId, method, responseEntity.getStatusCodeValue(), responseTime);
                    return response;
                })
                .onErrorResume(throwable -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    HttpResponse errorResponse = handleError(requestId, throwable, responseTime);
                    recordRequestStats(false, responseTime);
                    return Mono.just(errorResponse);
                })
                .retry(config.getMaxRetries());
    }

    // 私有辅助方法
    private URI buildUri(String url, RequestConfig config) {
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(url);

        if (config.getQueryParams() != null) {
            config.getQueryParams().forEach(uriBuilder::queryParam);
        }

        return uriBuilder.build().toUri();
    }

    private void addCustomHeaders(HttpHeaders headers, RequestConfig config) {
        if (config.getHeaders() != null) {
            config.getHeaders().forEach(headers::add);
        }
    }

    private Map<String, String> extractHeaders(org.springframework.http.HttpHeaders headers) {
        Map<String, String> result = new java.util.HashMap<>();
        headers.forEach((key, values) -> result.put(key, String.join(", ", values)));
        return result;
    }

    private HttpResponse handleError(String requestId, Throwable throwable, long responseTime) {
        String errorMessage;
        int statusCode = 0;

        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            statusCode = ex.getStatusCode().value();
            errorMessage = String.format("HTTP错误: %d - %s", statusCode, ex.getStatusText());
            logger.error("[{}] HTTP请求失败: {}", requestId, errorMessage);
        } else if (throwable instanceof java.util.concurrent.TimeoutException) {
            errorMessage = "请求超时";
            logger.error("[{}] 请求超时", requestId);
        } else {
            errorMessage = throwable.getMessage();
            logger.error("[{}] 请求异常: {}", requestId, errorMessage, throwable);
        }

        return new HttpResponse(requestId, false, statusCode, null, errorMessage, responseTime, null);
    }

    private String generateRequestId(String method, String url) {
        return String.format("FULL-ASYNC-%s-%d-%d",
                method, System.currentTimeMillis(), requestCounter.incrementAndGet());
    }

    private void recordRequestStats(boolean success, long responseTime) {
        String statsKey = "global";
        RequestStats stats = requestStats.computeIfAbsent(statsKey, k -> new RequestStats());

        stats.totalRequests++;
        stats.totalResponseTime += responseTime;

        if (success) {
            stats.successRequests++;
        } else {
            stats.failedRequests++;
        }
    }

    /**
     * 获取请求统计信息
     */
    public Map<String, Object> getRequestStats() {
        RequestStats stats = requestStats.get("global");
        if (stats == null) {
            return Map.of();
        }

        long avgResponseTime = stats.totalRequests > 0 ? stats.totalResponseTime / stats.totalRequests : 0;

        return Map.of(
                "totalRequests", stats.totalRequests,
                "successRequests", stats.successRequests,
                "failedRequests", stats.failedRequests,
                "successRate", stats.totalRequests > 0 ?
                        (double) stats.successRequests / stats.totalRequests : 0,
                "averageResponseTime", avgResponseTime
        );
    }

    /**
     * 使用示例
     */
    public static void main(String[] args) throws InterruptedException {
        FullyAsyncHttpClient client = new FullyAsyncHttpClient();
        RequestConfig config = RequestConfig.builder()
                .timeout(Duration.ofSeconds(10))
                .maxRetries(2)
                .build();

        // 方式1: 使用回调
        client.getAsync("https://httpbin.org/get", config, new ResponseCallback() {
            @Override
            public void onSuccess(HttpResponse response) {
                System.out.println("回调成功: " + response);
            }

            @Override
            public void onError(HttpResponse response) {
                System.out.println("回调失败: " + response.getErrorMessage());
            }
        });

        // 方式2: 使用响应式Mono
        client.getAsync("https://httpbin.org/get", config)
                .subscribe(response -> {
                    if (response.isSuccess()) {
                        System.out.println("Mono成功: " + response.getBody());
                    } else {
                        System.out.println("Mono失败: " + response.getErrorMessage());
                    }
                });

        // 方式3: 使用CompletableFuture
        client.getAsyncFuture("https://httpbin.org/get", config)
                .thenAccept(response -> {
                    if (response.isSuccess()) {
                        System.out.println("Future成功: " + response.getBody());
                    } else {
                        System.out.println("Future失败: " + response.getErrorMessage());
                    }
                });

        // 等待异步操作完成
        Thread.sleep(5000);
    }
}