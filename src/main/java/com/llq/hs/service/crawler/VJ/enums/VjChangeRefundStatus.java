package com.llq.hs.service.crawler.VJ.enums;

/**
 * <AUTHOR>
 * @version 2025/07/10
 **/
public enum VjChangeRefundStatus {

    ALLOW(0,"允许"),
    NOT_ALLOW(1,"不允许"),
    ;

    private Integer code;
    private String desc;


    VjChangeRefundStatus(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }


    public Integer getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }
}
