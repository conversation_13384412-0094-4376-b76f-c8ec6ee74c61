package com.llq.hs.service.crawler.module.hangsi.impl;

import com.llq.hs.service.crawler.common.constanst.HangSiConst;
import com.llq.hs.service.crawler.dispatch.crawler.CrawlerDispatchCenter;
import com.llq.hs.service.crawler.dispatch.crawler.dto.HangSiServiceDto;
import com.llq.hs.service.crawler.dispatch.hangsi.HangSiDispatchCenter;
import com.llq.hs.service.crawler.dispatch.hangsi.service.BaseAirlineService;
import com.llq.hs.service.crawler.mq.consumer.MqMessageConfirm;
import com.llq.hs.service.crawler.mq.consumer.RabbitAbstractMesageDispose;
import com.llq.hs.service.crawler.mq.producer.MessageService;
import com.llq.hs.service.crawler.mq.rep.Lcc_ZhengCe;
import com.llq.hs.service.crawler.mq.req.SaasSysRequestMessage;
import com.rabbitmq.client.Channel;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description: S7航司服务实现类
 * @date 2025/11/3 16:47
 */
@Service
public class S7ServiceImpl extends RabbitAbstractMesageDispose implements BaseAirlineService {
    private static final Logger logger = LoggerFactory.getLogger(S7ServiceImpl.class);
    @Resource
    private MessageService messageService;
    @Resource
    private CrawlerDispatchCenter crawlerDispatchCenter;
    // 注入线程池
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Override
    public void processMessage(SaasSysRequestMessage message, Channel channel, Message amqpMessage,String hangSi) {
        try {
            // 通过爬虫调度中心执行不同的爬虫任务
            //Lcc_ZhengCe priceResult = crawlerDispatchCenter.dispatchCrawler(hangSi, "PRICE", new HangSiServiceDto());
            MqMessageConfirm mqMessageConfirm = new MqMessageConfirm();
            mqMessageConfirm.setAmqpMessage(amqpMessage);
            mqMessageConfirm.setChannel(channel);
            mqMessageConfirm.setHangSi(hangSi);
            Lcc_ZhengCe policyResult = crawlerDispatchCenter.dispatchCrawler(hangSi, "", new HangSiServiceDto(),mqMessageConfirm);

            // 处理爬虫结果
//            if (priceResult != null) {
//                // 手动确认消息
//                confirmManuallyMessage(channel,amqpMessage,hangSi);
//                logger.info("{}消息处理成功并确认",hangSi);
//
//                messageService.sendMessage(priceResult);
//                logger.info("{}票价爬虫结果已发送",hangSi);
//            }else{
//                //无爬虫响应结果消息重新放入队列
//                basicNackRetrun(channel,amqpMessage,hangSi);
//            }

//            if (policyResult != null) {
//                // 手动确认消息
//                confirmManuallyMessage(channel,amqpMessage,hangSi);
//                logger.info("{}消息处理成功并确认",hangSi);
//
//                messageService.sendMessage(policyResult);
//                logger.info("{}政策爬虫结果已发送",hangSi);
//
//            }else{
//                //无爬虫响应结果消息重新放入队列
//                basicNackRetrun(channel,amqpMessage,hangSi);
//            }

            //调用爬虫-爬虫响应异步-直接确认消息消费
            confirmManuallyMessage(channel,amqpMessage,hangSi);
            logger.info("{}消息处理成功并确认",hangSi);
        } catch (Exception e) {
            logger.error("{}消息处理失败: {}", hangSi,e.getMessage());
            // 处理失败，拒绝消息并将其重新入队
            basicNackRetrun(channel,amqpMessage,hangSi);
        }
    }

//    @Override
//    public void processMessage(SaasSysRequestMessage message, Channel channel, Message amqpMessage, String hangSi) {
//        long deliveryTag = amqpMessage.getMessageProperties().getDeliveryTag();
//        try {
//
//
//            // 异步执行爬虫任务
//            taskExecutor.execute(() -> {
//                String threadName = Thread.currentThread().getName();
//                logger.info("{}开始异步爬虫任务(deliveryTag={}), 线程: {}", hangSi, deliveryTag, threadName);
//
//                try {
//                    // 执行爬虫任务
//                    Lcc_ZhengCe policyResult = crawlerDispatchCenter.dispatchCrawler(hangSi, "POLICY", new HangSiServiceDto());
//
//                    if (policyResult != null) {
//                        // 立即确认消息，让队列可以继续消费新消息
//                        confirmManuallyMessage(channel, amqpMessage, hangSi);
//                        logger.info("{}消息已确认(deliveryTag={})，开始异步处理", hangSi, deliveryTag);
//
//                        messageService.sendMessage(policyResult);
//                        logger.info("{}政策爬虫结果已发送(deliveryTag={})", hangSi, deliveryTag);
//                    } else {
//                        logger.warn("{}政策爬虫返回空结果(deliveryTag={})", hangSi, deliveryTag);
//                        // 这里可以加入重试机制或记录到死信队列
//                    }
//                } catch (Exception e) {
//                    logger.error("{}异步爬虫执行异常(deliveryTag={}): {}", hangSi, deliveryTag, e.getMessage(), e);
//                    // 异步异常处理，可以记录到重试队列或监控系统
//                } finally {
//                    logger.info("{}异步爬虫任务完成(deliveryTag={}), 线程: {}", hangSi, deliveryTag, threadName);
//                }
//            });
//
//        } catch (Exception e) {
//            logger.error("{}消息处理失败(deliveryTag={}): {}", hangSi, deliveryTag, e.getMessage(), e);
//            basicNackRetrun(channel, amqpMessage, hangSi);
//        }
//    }

    @Override
    public String getAirlineCode() {
        return HangSiConst.HANGSI_S7;
    }
}
