package com.llq.hs.service.crawler.dispatch.crawler;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.llq.hs.service.crawler.dispatch.crawler.dto.HangSiServiceDto;
import com.llq.hs.service.crawler.dispatch.crawler.service.BaseCrawlerService;
import com.llq.hs.service.crawler.mq.consumer.MqMessageConfirm;
import com.llq.hs.service.crawler.mq.rep.Lcc_ZhengCe;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @description: 爬虫调度中心，由调度中心对不同爬虫服务进行分配调用
 *  * 新增爬虫服务只需要新增对应的impl实现类并实现BaseAirlineService接口即可
 *  * 增加之后调度中心会自动将其注册（注入）
 * @date 2025/11/4
 */
@Component
public class CrawlerDispatchCenter {
    private static final Logger logger = LoggerFactory.getLogger(CrawlerDispatchCenter.class);

    private final Map<String, BaseCrawlerService> crawlerServiceMap = new ConcurrentHashMap<>();

    /**
     * 通过构造函数自动注入所有爬虫服务
     */
    @Autowired
    public CrawlerDispatchCenter(List<BaseCrawlerService> baseCrawlerServices) {
        for (BaseCrawlerService service : baseCrawlerServices) {
            StringBuffer buffer = new StringBuffer();
            if(StrUtil.isNotBlank(service.getCrawlerName())){
                buffer.append(service.getSupportedAirline().toUpperCase()).append("_").append(service.getCrawlerName());
            }else{
                buffer.append(service.getSupportedAirline().toUpperCase());
            }
            crawlerServiceMap.put(buffer.toString(), service);
            logger.info("注册爬虫服务: {} -> {}", service.getSupportedAirline(), service.getCrawlerName());
        }
        logger.info("爬虫服务注册完成，共注册 {} 个服务", crawlerServiceMap.size());
    }

   /**
    * @description:  爬虫服务调度中心
    * @param: airlineCode--航司
              crawlerType--爬虫服务类型
              hangSiServiceDto--航司服务入参参数
    * @return: com.llq.hs.service.crawler.mq.rep.Lcc_ZhengCe--爬虫服务响应结果数据
    * <AUTHOR>
    * @date: 2025/11/4 16:08
    */
    public Lcc_ZhengCe dispatchCrawler(String hangSi, String crawlerType, HangSiServiceDto hangSiServiceDto, MqMessageConfirm mqMessageConfirm) {
        StringBuffer buffer = new StringBuffer();
        if (ObjectUtil.isNull(hangSiServiceDto)  || StrUtil.isBlank(hangSi) ) {
            logger.warn("爬虫调度参数为空: airlineCode={}, crawlerType={}", hangSi, crawlerType);
            return null;
        }

        if(StrUtil.isNotBlank(crawlerType)){
            buffer.append(hangSi.toUpperCase()).append("_").append(crawlerType.toUpperCase());
        }else{
            buffer.append(hangSi.toUpperCase());
        }

        BaseCrawlerService baseCrawlerService = crawlerServiceMap.get(buffer.toString());

        if (baseCrawlerService != null) {
            logger.info("开始执行爬虫服务: {} - {}", hangSi, crawlerType);
            try {
                return baseCrawlerService.executeCrawler(hangSiServiceDto,hangSi,mqMessageConfirm);
            } catch (Exception e) {
                logger.error("爬虫服务执行失败: {} - {}, 错误: {}", hangSi, crawlerType, e.getMessage());
                return null;
            }
        } else {
            logger.warn("未找到对应的爬虫服务: {}", buffer);
            return null;
        }
    }

    /**
     * 获取支持的爬虫服务列表
     */
    public Map<String, BaseCrawlerService> getCrawlerServices() {
        return new ConcurrentHashMap<>(crawlerServiceMap);
    }
}