package com.llq.hs.service.crawler.dispatch.hangsi.service;

import com.llq.hs.service.crawler.mq.req.SaasSysRequestMessage;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;

/**
 * <AUTHOR>
 * @description: 航司服务基础接口
 * @date 2025/11/3 17:07
 */
public interface BaseAirlineService {
    void processMessage(SaasSysRequestMessage message, Channel channel, Message amqpMessage,String hangSi);
    String getAirlineCode();
}
