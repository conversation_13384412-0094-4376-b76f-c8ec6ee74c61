package com.llq.hs.service.crawler.VJ.service.impl;


import com.llq.hs.service.crawler.VJ.dto.VjResultDataDto;
import com.llq.hs.service.crawler.VJ.dto.VjResultPriceDataDto;
import com.llq.hs.service.crawler.VJ.dto.VjResultPriceDataSegmentDto;
import com.llq.hs.service.crawler.VJ.entity.Lcc_ZhengCe;
import com.llq.hs.service.crawler.VJ.entity.Lcc_ZhengCe_HangDuan;
import com.llq.hs.service.crawler.VJ.enums.AirLineType;
import com.llq.hs.service.crawler.VJ.pool.Timer_Pool_Lcc_ZhengCe;
import com.llq.hs.service.crawler.VJ.service.Lcc_VjSaveDataService;
import com.llq.hs.service.crawler.VJ.util.LccDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025/07/10
 **/
@Slf4j
@Service
public class Lcc_VjSaveDataServiceImpl implements Lcc_VjSaveDataService {

    //private static final BlockingQueue<List<Zx_ZhengCe>> policyQueue = new LinkedBlockingQueue<>(Integer.MAX_VALUE);

    @Resource
    private Timer_Pool_Lcc_ZhengCe timerPoolLccZhengCe;


    /**
     * 保存航班数据
     *
     * @param dataDtoList
     * @param logId
     */
    @Override
    public void     saveOneWay(List<VjResultDataDto> dataDtoList, Long logId) {
        log.debug("Lcc_VJSaveDataService-save-{}", logId);
        for (VjResultDataDto s7ResultDataDto: dataDtoList) {
            List<Lcc_ZhengCe> entityList = buildDto2Entity(s7ResultDataDto,logId);
            entityList.stream().forEach(zhengCe -> {
                List<Lcc_ZhengCe_HangDuan> lcc_hangDuanList_go = zhengCe.getLcc_hangDuanList_go();
               long count = lcc_hangDuanList_go.stream()
                        .filter(hangDuan -> !hangDuan.getFlightNumber()
                                .startsWith(AirLineType.VJ.getCode())&& !hangDuan.getFlightNumber()
                                .startsWith("VZ"))
                        .count();
               // 如果此政策航段不包含非S7航班，则保存此政策
               if (count == 0){
                   timerPoolLccZhengCe.store(zhengCe);
               }
            });
        }
    }

    /**
     * 构建返回参数
     * @param dataDtoList
     * @return
     */
    @Override
    public List<Lcc_ZhengCe> getOneWay(List<VjResultDataDto> dataDtoList) {
        List<Lcc_ZhengCe>  zhengCeList = new ArrayList<>();
        for (VjResultDataDto s7ResultDataDto: dataDtoList) {
            List<Lcc_ZhengCe> entityList = buildDto2Entity(s7ResultDataDto,0L);
            entityList.stream().forEach(zhengCe -> {
                List<Lcc_ZhengCe_HangDuan> lcc_hangDuanList_go = zhengCe.getLcc_hangDuanList_go();
                long count = lcc_hangDuanList_go.stream()
                        .filter(hangDuan -> !hangDuan.getFlightNumber()
                                .startsWith(AirLineType.VJ.getCode())&& !hangDuan.getFlightNumber()
                                .startsWith("VZ"))
                        .count();
                // 如果此政策航段不包含非VJ航班，则保存此政策
                if (count == 0){
                    zhengCeList.add(zhengCe);
                }
            });
        }
        return zhengCeList;
    }



    /**
     * 构建政策数据
     *
     * @param s7ResultDataDto
     * @param logId
     * @return
     */
    private List<Lcc_ZhengCe> buildDto2Entity(VjResultDataDto s7ResultDataDto, Long logId) {
        List<Lcc_ZhengCe> rssPolicyList = new ArrayList<>();
        //获取航段
        //构建基础政策
        VjResultPriceDataDto basicPriceDto = s7ResultDataDto.getBasicPriceDto();
        if (null != basicPriceDto) {
            List<VjResultPriceDataSegmentDto> segmentDtoList_go = basicPriceDto.getPriceDataSegmentDtoList_go();
            Lcc_ZhengCe rssPolicy = getZxZhengCe(s7ResultDataDto, segmentDtoList_go, basicPriceDto);
            rssPolicyList.add(rssPolicy);
        }
        //构建标准政策
        VjResultPriceDataDto standardPriceDto = s7ResultDataDto.getStandardPriceDto();
        if (null != standardPriceDto) {
            List<VjResultPriceDataSegmentDto> segmentDtoList_go = standardPriceDto.getPriceDataSegmentDtoList_go();
            Lcc_ZhengCe rssPolicy = getZxZhengCe(s7ResultDataDto, segmentDtoList_go, standardPriceDto);
            rssPolicyList.add(rssPolicy);
        }
        return rssPolicyList;
    }

    /**
     * 获取政策对象
     *
     * @param s7ResultDataDto
     * @param segmentDtoList
     * @param basicPriceDto
     * @return
     */
    private Lcc_ZhengCe getZxZhengCe(VjResultDataDto s7ResultDataDto,
                                     List<VjResultPriceDataSegmentDto> segmentDtoList,
                                     VjResultPriceDataDto basicPriceDto) {
        Lcc_ZhengCe lcc_zhengCe = buildPolicy(s7ResultDataDto, basicPriceDto);
        List<Lcc_ZhengCe_HangDuan> lcc_zhengCe_hangDuans = segmentDtoList.stream().map(segmentDto -> {
            Lcc_ZhengCe_HangDuan hangDuan = new Lcc_ZhengCe_HangDuan();
            hangDuan.setFlightNumber(segmentDto.getFlightNumber());
            hangDuan.setDepAirport(segmentDto.getDepAirport());
            hangDuan.setArrAirport(segmentDto.getArrAirport());
            hangDuan.setDepTime(segmentDto.getDepTime());
            hangDuan.setArrTime(segmentDto.getArrTime());
            hangDuan.setSeatClass(segmentDto.getSeatClass());
            hangDuan.setFareBasis(segmentDto.getFareBasis());
            return hangDuan;
        }).collect(Collectors.toList());
        lcc_zhengCe.setLcc_hangDuanList_go(lcc_zhengCe_hangDuans);
        return lcc_zhengCe;
    }


    /**
     * 构建政策
     *
     * @param dataDto
     * @param priceDataDto
     * @return
     */
    private Lcc_ZhengCe buildPolicy(VjResultDataDto dataDto, VjResultPriceDataDto priceDataDto) {
        Lcc_ZhengCe lcc_zhengCe = new Lcc_ZhengCe();
        lcc_zhengCe.setOriginalCurrency(priceDataDto.getOriginalCurrency());
        lcc_zhengCe.setExchangeRate(LccDataUtil.transIntAmount(priceDataDto.getExchangeRate(),6));
        lcc_zhengCe.setDepCity(dataDto.getDepCity());
        lcc_zhengCe.setArrCity(dataDto.getArrCity());
        lcc_zhengCe.setSeatCount(priceDataDto.getSeatCount());
        lcc_zhengCe.setDateStr_go(dataDto.getDateStr_go());
        //lcc_zhengCe.setDateStr_back("2025-04-29");
        lcc_zhengCe.setBasePrice(LccDataUtil.transIntAmount(priceDataDto.getBasePrice()));
        lcc_zhengCe.setTaxFeeAount(LccDataUtil.transIntAmount(priceDataDto.getTaxFeeAount()));
        lcc_zhengCe.setXingLiPiece(priceDataDto.getBaggageQuantity());
        lcc_zhengCe.setXingLiWeight(priceDataDto.getBaggageWeight());
        //LCC_VJ = 129
        lcc_zhengCe.setCreateByOrgId(129);
        //LCC_VJ = 165
        lcc_zhengCe.setCreateByMemberId(165);
        lcc_zhengCe.setCreateTime(new Date());
        lcc_zhengCe.setNoChange(priceDataDto.getNoChange());//可以改期
        //lcc_zhengCe.setChangeHour(4);
        lcc_zhengCe.setChangeFee(LccDataUtil.transIntAmount(priceDataDto.getChangeFee()));
        lcc_zhengCe.setNoRefund(priceDataDto.getNoRefund());//可以退票
        //lcc_zhengCe.setRefundHour(5);
        lcc_zhengCe.setRefundFee(LccDataUtil.transIntAmount(priceDataDto.getRefundFee()));

        if ( null == priceDataDto.getRefundFee()){
            lcc_zhengCe.setNoRefund(1);
        }

        //lcc_zhengCe.setNoChange2(0);//可以改期
        //lcc_zhengCe.setChangeHour2(4);
        //lcc_zhengCe.setChangeFee2(300.0);
        //lcc_zhengCe.setNoRefund2(0);//可以退票
        //lcc_zhengCe.setRefundHour2(5);
        //lcc_zhengCe.setRefundFee2(350.0);

        Integer xingLi = lcc_zhengCe.getXingLiPiece();
        //5、VJ无行李产品，6、VJ有行李产品
        lcc_zhengCe.setProductType(xingLi > 0 ? 5 : 6);

        lcc_zhengCe.setHangSi(AirLineType.VJ.getCode());

        return lcc_zhengCe;
    }


}
