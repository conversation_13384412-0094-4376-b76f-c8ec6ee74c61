package com.llq.hs.service.crawler.VJ.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Strings;
import com.llq.hs.service.crawler.VJ.dto.MailDataDto;
import com.llq.hs.service.crawler.VJ.dto.MailDto;
import com.llq.hs.service.crawler.VJ.entity.LccMailAttachment;
import com.llq.hs.service.crawler.VJ.entity.LccMailEntity;
import com.llq.hs.service.crawler.VJ.enums.AirLineType;
import com.llq.hs.service.crawler.VJ.enums.MailType;
import com.llq.hs.service.crawler.VJ.service.LccMailAttachmentService;
import com.llq.hs.service.crawler.VJ.service.LccMailEntityService;
import com.llq.hs.service.crawler.VJ.service.Sys_LogService;
import com.llq.hs.service.crawler.VJ.util.MailUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/07/12
 **/
@Component
@Slf4j
public class Timer_LCC_GetMail {

    @Resource
    private LccMailEntityService lccMailEntityService;
    @Resource
    private LccMailAttachmentService lccMailAttachmentService;

    @Resource
    private Sys_LogService logService;

    //private static final int BATCH_SIZE = 100;

    //航变邮件
    private static final String CHANGE_FROM = "<EMAIL>";

    private static final String OUT_FROM = "<EMAIL>";

    //配置外网地址
    @Value("${lcc.mail.server}")
    private String SERVER;


    /**
     * 获取单程航线数据
     * 5分钟*60秒*1000毫秒
     * 定期获取
     */
    //@Scheduled(initialDelay = 1000, fixedRate = 10 * 60 * 1000)
    public void queryMailData() {
        log.debug("Timer_LCC_GetMail-queryMailData-start");
        //获取上次收件日期 MAX(receiveDate) as receiveDate
        Date startDate = lccMailEntityService.selectMaxReceiveDate(AirLineType.VJ.getCode());
        if ( null == startDate) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -30);
            startDate = calendar.getTime();
        }
        Date endDate = new Date();
        MailDto mailDto = MailUtil.receiveBetween(AirLineType.VJ, startDate, endDate);
        if (mailDto.isSuccess()) {
            saveMailData(mailDto);
        }
        log.debug("Timer_LCC_GetMail-queryMailData-end");
    }

    /**
     * 保存邮件
     *
     * @param mailDto
     */
    private void saveMailData(MailDto mailDto) {
       /* Sys_Log sysLog = new Sys_Log();
        sysLog.setLogMemberId(141);//以系统中LCC用户的名义发布和更新政策
        sysLog.setLogModuleId(435);//算是在采集政策接口模块中进行的操作
        sysLog.setLogTime(new Date());
        sysLog.setLogTableNames("lcc_mail");//操作可能会对这些表产生影响
        logService.save(sysLog);*/

        long logId = 0;

        List<MailDataDto> data = mailDto.getData();

        Date now = new Date();
        data.stream().filter(mailDataDto -> {
            //去掉重复数据
            if (!Strings.isNullOrEmpty(mailDataDto.getMsgId())){
                long cnt = lccMailEntityService.count(Wrappers.
                        <LccMailEntity>lambdaQuery()
                        .eq(LccMailEntity::getMsgId, mailDataDto.getMsgId()));
                return cnt == 0;
            }
            return true;
        }).forEach(dto -> {
            LccMailEntity entity = new LccMailEntity();
            entity.setAirline(AirLineType.VJ.getCode());
            entity.setMsgId(dto.getMsgId());
            entity.setRecipient(dto.getFrom());
            entity.setSubject(dto.getSubject());
            entity.setSendDate(dto.getSendDate());
            entity.setReceiveDate(dto.getReceiveDate());
            entity.setContent(dto.getContent());
            entity.setLogId(logId);
            // 区分航变出票
            if (CHANGE_FROM.contains(dto.getFrom())) {
                entity.setMailType(MailType.AIR_CHANGE.getCode());
            } else if (OUT_FROM.contains(dto.getFrom())) {
                entity.setMailType(MailType.OUT_TICKET.getCode());
            } else {
                entity.setMailType(MailType.OTHER.getCode());
            }
            entity.setCreateTime(now);
            entity.setCreateByMemberId(141);
            entity.setLogType(1);//新增
            //未处理
            entity.setStatus("N");

            //文件服务地址
            entity.setServer(SERVER);

            lccMailEntityService.save(entity);
            // 保存附件
            dto.getAttachments().stream()
                    .forEach(mailFileDataDto -> {
                        LccMailAttachment attachment = new LccMailAttachment();
                        attachment.setMailId(entity.getId());
                        attachment.setFileName(mailFileDataDto.getFileName());
                        attachment.setFileSize(mailFileDataDto.getSize());
                        attachment.setFileUrl(mailFileDataDto.getFileUrl());
                        attachment.setCreateTime(now);
                        lccMailAttachmentService.save(attachment);
                    });
        });
    }
}
