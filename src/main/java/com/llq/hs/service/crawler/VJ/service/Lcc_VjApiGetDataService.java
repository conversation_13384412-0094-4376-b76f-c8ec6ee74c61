package com.llq.hs.service.crawler.VJ.service;

import com.llq.hs.service.crawler.VJ.dto.VjResultDto;
import com.llq.hs.service.crawler.VJ.vo.VjApiResultVo;

/**
 * API
 * 获取数据
 */
public interface Lcc_VjApiGetDataService {

    /**
     * 查询数据
     * @param depCity
     * @param arrCity
     * @param departureDate
     * @param currency
     * @param seq
     * @return
     */
    VjResultDto getOneWayS7TicketData(String depCity, String arrCity, String departureDate,
                                      String currency, int seq);

    VjResultDto getOneWayS7TicketData(String depCity, String arrCity, String departureDate,
                                      String currency, VjApiResultVo apiResultVo);
}
