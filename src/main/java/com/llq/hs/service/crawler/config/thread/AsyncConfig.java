package com.llq.hs.service.crawler.config.thread;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @description: 线程池配置类
 * @date 2025/11/3 14:00
 */
@Configuration
public class AsyncConfig {
    private static final Logger logger = LoggerFactory.getLogger(AsyncConfig.class);
    //    @Value("${thread.task.core}")
//    private int coreSize;
//
//    @Value("${thread.task.max}")
//    private int maxSize;
//
//    @Value("${thread.task.queue}")
//    private int queueSize;
//    @Bean
//    public ThreadPoolTaskExecutor vjTaskExecutor() {
//
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        // 核心线程数
//        executor.setCorePoolSize(coreSize);
//        // 最大线程数
//        executor.setMaxPoolSize(maxSize);
//        // 队列大小
//        executor.setQueueCapacity(queueSize);
//        // 线程名称前缀
//        executor.setThreadNamePrefix("VJ-Executor-");
//        // 拒绝策略
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        // 初始化
//        executor.initialize();
//        logger.info("VJ-线程池初始化完成: 核心线程={}, 最大线程={}, 队列容量={}",
//                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
//        return executor;
//    }
//    @Bean
//    public ThreadPoolTaskExecutor s7TaskExecutor() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        // 核心线程数
//        executor.setCorePoolSize(coreSize);
//        // 最大线程数
//        executor.setMaxPoolSize(maxSize);
//        // 队列大小
//        executor.setQueueCapacity(queueSize);
//        // 线程名称前缀
//        executor.setThreadNamePrefix("S7-Executor-");
//        // 拒绝策略
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        // 初始化
//        executor.initialize();
//        logger.info("S7-线程池初始化完成: 核心线程={}, 最大线程={}, 队列容量={}",
//                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
//        return executor;
//    }
    @Bean
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        int cpuCores = Runtime.getRuntime().availableProcessors();
        // 根据系统资源动态调整-IO密集型
        //核心线程数
        executor.setCorePoolSize(cpuCores*2);
        //最大线程数
        executor.setMaxPoolSize(cpuCores*4);
        //队列任务数
        executor.setQueueCapacity(cpuCores*8);
        // 线程名称前缀
        executor.setThreadNamePrefix("taskExecutor-");
        //空闲线程存活时间（秒）
        executor.setKeepAliveSeconds(60);
        // 自定义拒绝策略
        executor.setRejectedExecutionHandler((r, exec) -> {
            logger.error("线程池已满，拒绝任务。活跃线程: {}, 队列大小: {}, 最大线程: {}",
                    exec.getActiveCount(), exec.getQueue().size(), exec.getMaximumPoolSize());
            // 发送告警、记录详细日志
            monitorThreadPool(exec);
            throw new RejectedExecutionException("线程池已满，任务被拒绝");
        });
        //true：线程池会等待所有正在执行的任务完成后再完全关闭。
        executor.setWaitForTasksToCompleteOnShutdown(true);
        //指定线程池在关闭时等待所有任务完成的最大时间
        executor.setAwaitTerminationSeconds(120);
        executor.initialize();
        logger.info("线程池初始化完成: 核心线程={}, 最大线程={}, 队列容量={}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        return executor;
    }

    private void monitorThreadPool(ThreadPoolExecutor exec) {
        logger.warn("线程池监控 - 活跃: {}, 队列: {}/{}, 完成: {}",
                exec.getActiveCount(),
                exec.getQueue().size(),
                exec.getQueue().remainingCapacity() + exec.getQueue().size(),
                exec.getCompletedTaskCount());
    }
}
