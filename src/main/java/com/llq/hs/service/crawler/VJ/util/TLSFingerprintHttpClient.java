package com.llq.hs.service.crawler.VJ.util;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;
import java.util.*;

public class TLSFingerprintHttpClient {

    public static class TLSFingerprint {
        private String fingerprintId;
        private List<String> protocols;
        private List<String> cipherSuites;
        private String ja3Hash;

        // Getters and setters
        public String getFingerprintId() { return fingerprintId; }
        public void setFingerprintId(String fingerprintId) { this.fingerprintId = fingerprintId; }

        public List<String> getProtocols() { return protocols; }
        public void setProtocols(List<String> protocols) { this.protocols = protocols; }

        public List<String> getCipherSuites() { return cipherSuites; }
        public void setCipherSuites(List<String> cipherSuites) { this.cipherSuites = cipherSuites; }

        public String getJa3Hash() { return ja3Hash; }
        public void setJa3Hash(String ja3Hash) { this.ja3Hash = ja3Hash; }
    }

    /**
     * 捕获目标网站的TLS指纹信息
     * @param targetUrl 目标URL
     * @return TLS指纹信息
     */
    public static TLSFingerprint captureTargetTLSFingerprint(String targetUrl) {
        TLSFingerprint fingerprint = new TLSFingerprint();

        try {
            // 创建SSL上下文
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(new TrustSelfSignedStrategy())
                    .build();

            // 获取SSLSocketFactory
            SSLSocketFactory socketFactory = sslContext.getSocketFactory();

            // 从URL中提取主机和端口
            String host = extractHost(targetUrl);
            int port = targetUrl.startsWith("https://") ? 443 : 80;

            // 创建SSL Socket进行握手
            SSLSocket socket = (SSLSocket) socketFactory.createSocket(host, port);

            // 获取支持的协议和加密套件
            String[] supportedProtocols = socket.getSupportedProtocols();
            String[] supportedCipherSuites = socket.getSupportedCipherSuites();

            // 设置启用的协议和加密套件（模拟浏览器行为）
            List<String> enabledProtocols = new ArrayList<>();
            for (String protocol : supportedProtocols) {
                if (protocol.startsWith("TLSv1.2") || protocol.startsWith("TLSv1.3")) {
                    enabledProtocols.add(protocol);
                }
            }

            // 按照常见浏览器的顺序排列加密套件
            List<String> enabledCipherSuites = filterCipherSuites(supportedCipherSuites);

            socket.setEnabledProtocols(enabledProtocols.toArray(new String[0]));
            socket.setEnabledCipherSuites(enabledCipherSuites.toArray(new String[0]));

            // 开始握手
            socket.startHandshake();

            // 获取实际启用的协议和加密套件
            String[] negotiatedProtocols = socket.getEnabledProtocols();
            String[] negotiatedCipherSuites = socket.getEnabledCipherSuites();

            // 设置指纹信息
            fingerprint.setProtocols(Arrays.asList(negotiatedProtocols));
            fingerprint.setCipherSuites(Arrays.asList(negotiatedCipherSuites));

            // 生成JA3指纹
            String ja3 = generateJA3Fingerprint(negotiatedProtocols, negotiatedCipherSuites);
            fingerprint.setJa3Hash(ja3);

            // 生成指纹ID
            fingerprint.setFingerprintId(UUID.randomUUID().toString());

            socket.close();

        } catch (Exception e) {
            e.printStackTrace();
        }

        return fingerprint;
    }

    /**
     * 从URL中提取主机名
     * @param url URL地址
     * @return 主机名
     */
    private static String extractHost(String url) {
        String host = url.replace("https://", "").replace("http://", "");
        if (host.contains("/")) {
            host = host.substring(0, host.indexOf("/"));
        }
        if (host.contains(":")) {
            host = host.substring(0, host.indexOf(":"));
        }
        return host;
    }

    /**
     * 过滤并排序加密套件，模拟现代浏览器的行为
     * @param supportedCipherSuites 支持的加密套件
     * @return 过滤后的加密套件列表
     */
    private static List<String> filterCipherSuites(String[] supportedCipherSuites) {
        List<String> result = new ArrayList<>();

        // 优先选择安全性高且常见的加密套件
        String[] preferredCipherSuites = {
                "TLS_AES_128_GCM_SHA256",
                "TLS_AES_256_GCM_SHA384",
                "TLS_CHACHA20_POLY1305_SHA256",
                "ECDHE-ECDSA-AES128-GCM-SHA256",
                "ECDHE-RSA-AES128-GCM-SHA256",
                "ECDHE-ECDSA-AES256-GCM-SHA384",
                "ECDHE-RSA-AES256-GCM-SHA384",
                "ECDHE-ECDSA-CHACHA20-POLY1305",
                "ECDHE-RSA-CHACHA20-POLY1305",
                "ECDHE-RSA-AES128-SHA",
                "ECDHE-RSA-AES256-SHA",
                "AES128-GCM-SHA256",
                "AES256-GCM-SHA384",
                "AES128-SHA",
                "AES256-SHA"
        };

        // 先添加首选的加密套件
        for (String preferred : preferredCipherSuites) {
            for (String supported : supportedCipherSuites) {
                if (supported.equals(preferred) && !result.contains(supported)) {
                    result.add(supported);
                }
            }
        }

        // 添加其他支持的加密套件
        for (String supported : supportedCipherSuites) {
            if (!result.contains(supported) &&
                    !supported.contains("_NULL_") &&
                    !supported.contains("_EXPORT_") &&
                    !supported.contains("_anon_")) {
                result.add(supported);
            }
        }

        return result;
    }

    /**
     * 生成JA3指纹
     * @param protocols 协议列表
     * @param cipherSuites 加密套件列表
     * @return JA3指纹哈希
     */
    private static String generateJA3Fingerprint(String[] protocols, String[] cipherSuites) {
        // JA3指纹格式: TLSVersion,CipherSuites,Extensions,EllipticCurves,EllipticCurvePointFormats
        // 简化版本只包含前三个字段

        StringBuilder sb = new StringBuilder();

        // TLS版本 (简化处理)
        sb.append("771,"); // TLS 1.2的十六进制值为0x0303，十进制为771

        // 加密套件
        List<String> cipherIds = new ArrayList<>();
        for (String cipher : cipherSuites) {
            String id = getCipherSuiteId(cipher);
            if (id != null) {
                cipherIds.add(id);
            }
        }
        sb.append(String.join("-", cipherIds));
        sb.append(",");

        // 扩展 (简化处理)
        sb.append("0-10-11");

        return sb.toString();
    }

    /**
     * 获取加密套件的ID
     * @param cipherSuite 加密套件名称
     * @return ID字符串
     */
    private static String getCipherSuiteId(String cipherSuite) {
        // 这里只是一个简化的映射，实际应用中需要完整的映射表
        Map<String, String> cipherMap = new HashMap<>();
        cipherMap.put("TLS_AES_128_GCM_SHA256", "4865");
        cipherMap.put("TLS_AES_256_GCM_SHA384", "4866");
        cipherMap.put("TLS_CHACHA20_POLY1305_SHA256", "4867");
        cipherMap.put("ECDHE-ECDSA-AES128-GCM-SHA256", "49195");
        cipherMap.put("ECDHE-RSA-AES128-GCM-SHA256", "49199");
        cipherMap.put("ECDHE-ECDSA-AES256-GCM-SHA384", "49196");
        cipherMap.put("ECDHE-RSA-AES256-GCM-SHA384", "49200");

        return cipherMap.getOrDefault(cipherSuite, "0");
    }

    /**
     * 创建支持TLS指纹的HTTP客户端
     * @param proxyHost 代理主机
     * @param proxyPort 代理端口
     * @param proxyUser 代理用户名
     * @param proxyPass 代理密码
     * @return CloseableHttpClient实例
     */
    public static CloseableHttpClient createHttpClientNew(String proxy_host, int proxy_post,
                                                          String proxy_user, String proxy_pass) {
        try {
            HttpHost proxy = new HttpHost(proxy_host, proxy_post, "http");

            // 创建SSL上下文
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(new TrustSelfSignedStrategy())
                    .build();

            RequestConfig requestConfig = RequestConfig.custom()
                    .setProxy(proxy)
                    .setConnectTimeout(10000)
                    .setSocketTimeout(15000)
                    .setConnectionRequestTimeout(5000)
                    .build();
            PoolingHttpClientConnectionManager connectionManager =
                    new PoolingHttpClientConnectionManager();
            connectionManager.setMaxTotal(50);
            connectionManager.setDefaultMaxPerRoute(10);

            // 认证配置
            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(
                    new AuthScope(proxy_host, proxy_post),
                    new UsernamePasswordCredentials(proxy_user, proxy_pass)
            );
            // 创建SSL连接套接字工厂
            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(
                    sslContext,
                    new String[]{"TLSv1.2", "TLSv1.3"},
                    null,
                    NoopHostnameVerifier.INSTANCE
            );

            // 构建HTTP客户端
            return HttpClients.custom()
                    .setSSLSocketFactory(sslSocketFactory)
                    .setDefaultCredentialsProvider(credentialsProvider)
                    .setDefaultRequestConfig(requestConfig)
                    .build();

        } catch (Exception e) {
            e.printStackTrace();
            return HttpClients.createDefault();
        }
    }

    /**
     * 测试TLS指纹捕获
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        TLSFingerprint fingerprint = captureTargetTLSFingerprint("https://mproxy.api.s7.ru");
        System.out.println("Fingerprint ID: " + fingerprint.getFingerprintId());
        System.out.println("Protocols: " + fingerprint.getProtocols());
        System.out.println("Cipher Suites: " + fingerprint.getCipherSuites());
        System.out.println("JA3 Hash: " + fingerprint.getJa3Hash());
    }
}
