package com.llq.hs.service.crawler.VJ.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llq.hs.service.crawler.VJ.entity.LccSchedule;
import com.llq.hs.service.crawler.VJ.mapper.LccScheduleMapper;
import com.llq.hs.service.crawler.VJ.service.LccScheduleService;
import com.llq.hs.service.crawler.VJ.util.LccDataUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【lcc_schedule】的数据库操作Service实现
* @createDate 2025-07-12 16:01:36
*/
@Service
public class LccScheduleServiceImpl extends ServiceImpl<LccScheduleMapper, LccSchedule>
    implements LccScheduleService {


    /**
     * 记录任务
     * @param airCode
     * @param desc
     * @param taskCount
     * @param memberId
     * @param logId
     * @return
     */
    @Override
    public LccSchedule startTask(String airCode, String desc, int taskCount, Integer memberId, Long logId) {

        LccSchedule lccSchedule = new LccSchedule();
        lccSchedule.setAirline(airCode);
        lccSchedule.setRemark(desc);
        lccSchedule.setStartTime(new Date());
        lccSchedule.setTaskCount(taskCount);
        //运行中
        lccSchedule.setStatus(1);
        lccSchedule.setCreateByMemberId(memberId);
        lccSchedule.setCreateTime(new Date());
        // 新增
        lccSchedule.setLogId(logId);
        lccSchedule.setLogType(0);
        this.save(lccSchedule);
        return lccSchedule;

    }

    @Override
    public void endTask(LccSchedule lccSchedule) {
        lccSchedule.setEndTime(new Date());
        // 计算耗时 单位秒
        long costTime = ( lccSchedule.getEndTime().getTime()
                - lccSchedule.getStartTime().getTime()) / 1000;
        lccSchedule.setCostTime(costTime);
        //计算成功率
        double rate = lccSchedule.getSuccessCount() * 1.0
                / lccSchedule.getTaskCount() * 100;
        lccSchedule.setRate(LccDataUtil.scale(new BigDecimal(rate),2));
        //运行完成
        lccSchedule.setStatus(2);
        // 更新
        lccSchedule.setLogType(1);
        this.updateById(lccSchedule);
    }


    @Override
    public void stopTask(LccSchedule lccSchedule) {
        lccSchedule.setEndTime(new Date());
        // 计算耗时 单位秒
        long costTime = ( lccSchedule.getEndTime().getTime()
                - lccSchedule.getStartTime().getTime()) / 1000;
        lccSchedule.setCostTime(costTime);
        //运行中断
        lccSchedule.setStatus(3);
        // 更新
        lccSchedule.setLogType(1);
        this.updateById(lccSchedule);
    }


}




