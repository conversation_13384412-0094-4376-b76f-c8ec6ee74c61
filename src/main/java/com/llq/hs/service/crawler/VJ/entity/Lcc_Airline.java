package com.llq.hs.service.crawler.VJ.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName lcc_airLine
 */
@TableName(value ="lcc_airLine")
@Data
public class Lcc_Airline implements Serializable {
    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 航司二字码，如S7
     */
    @TableField(value = "hangSi")
    private String hangSi;

    /**
     * 币种三字码，如RUB
     */
    @TableField(value = "biZhong")
    private String biZhong;

    /**
     * 出发国家二字码，如RU
     */
    @TableField(value = "depCountry")
    private String depCountry;

    /**
     * 到达国家二字码，如CN
     */
    @TableField(value = "arrCountry")
    private String arrCountry;

    /**
     * 出发城市三字码，如MOW
     */
    @TableField(value = "depCity")
    private String depCity;

    /**
     * 到达城市三字码，如ABA
     */
    @TableField(value = "arrCity")
    private String arrCity;

    /**
     * 出发机场三字码，如DME
     */
    @TableField(value = "depAirport")
    private String depAirport;

    /**
     * 到达机场三字码，如BKK
     */
    @TableField(value = "arrAirport")
    private String arrAirport;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}