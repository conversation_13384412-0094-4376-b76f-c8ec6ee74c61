package com.llq.hs.service.crawler.VJ.dto;

/**
 * <AUTHOR>
 * @version 2025/07/07
 **/
public class VjTokenDataDto {


    /**
     * 线程池标识
     */
    private int pool;

    /**
     * 查询条件
     */
    private VjDataParamDto searchParams;


    /**
     * 代理地址
     */
    private VjProxyConfigDTO proxy_config;


    public int getPool() {
        return pool;
    }

    public void setPool(int pool) {
        this.pool = pool;
    }

    public VjDataParamDto getSearchParams() {
        return searchParams;
    }

    public void setSearchParams(VjDataParamDto searchParams) {
        this.searchParams = searchParams;
    }

    public VjProxyConfigDTO getProxy_config() {
        return proxy_config;
    }

    public void setProxy_config(VjProxyConfigDTO proxy_config) {
        this.proxy_config = proxy_config;
    }

    @Override
    public String toString() {
        return "S7TokenDataDto{" +
                "pool=" + pool +
                ", searchParams=" + searchParams +
                ", proxy_config=" + proxy_config +
                '}';
    }
}
