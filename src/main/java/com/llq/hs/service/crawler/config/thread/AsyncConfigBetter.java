package com.llq.hs.service.crawler.config.thread;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @description: 线程池配置类(CPU密集型、IO密集型)
 * @date 2025/11/3 14:00
 */
//@Configuration
public class AsyncConfigBetter {
    private static final Logger logger = LoggerFactory.getLogger(AsyncConfigBetter.class);
    /**
     * @description: CPU密集型线程池配置
     * <AUTHOR>
     * @date 2025/11/3 14:35
     */
    @Bean
    public ThreadPoolTaskExecutor vjTaskExecutorCpuIntensive() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数 = CPU核心数
        int cpuCores = Runtime.getRuntime().availableProcessors();
        executor.setCorePoolSize(cpuCores);
        // 最大线程数 = CPU核心数 * 2
        executor.setMaxPoolSize(cpuCores * 2);
        // 队列容量适中
        executor.setQueueCapacity(50);
        // 线程存活时间（秒）
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("VJ-CPU-Executor-");
        // 拒绝策略：丢弃并记录日志
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(120);
        executor.initialize();
        logger.info("VJ-CPU-线程池初始化完成: 核心线程={}, 最大线程={}, 队列容量={}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        return executor;
    }
    /**
     * @description: CPU密集型线程池配置
     * <AUTHOR>
     * @date 2025/11/3 14:35
     */
    @Bean
    public ThreadPoolTaskExecutor s7TaskExecutorCpuIntensive() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数 = CPU核心数
        int cpuCores = Runtime.getRuntime().availableProcessors();
        executor.setCorePoolSize(cpuCores);
        // 最大线程数 = CPU核心数 * 2
        executor.setMaxPoolSize(cpuCores * 2);
        // 队列容量适中
        executor.setQueueCapacity(50);
        // 线程存活时间（秒）
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("S7-CPU-Executor-");
        // 拒绝策略：丢弃并记录日志
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(120);
        executor.initialize();
        logger.info("S7-CPU-线程池初始化完成: 核心线程={}, 最大线程={}, 队列容量={}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        return executor;
    }

    /**
     * @description: IO密集型线程池配置
     * <AUTHOR>
     * @date 2025/11/3 14:35
     */
    @Bean
    public ThreadPoolTaskExecutor vjTaskExecutorIoIntensive() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        int cpuCores = Runtime.getRuntime().availableProcessors();
        // IO密集型可以设置更多线程
        executor.setCorePoolSize(cpuCores * 2);
        executor.setMaxPoolSize(cpuCores * 4);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(120);
        executor.setThreadNamePrefix("VJ-IO-Executor-");
        // 拒绝策略：自定义处理
        executor.setRejectedExecutionHandler(new RejectedExecutionHandler() {
            private final Logger logger = LoggerFactory.getLogger(getClass());
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                logger.warn("任务被拒绝，当前活跃线程: {}, 队列大小: {}",
                        executor.getActiveCount(), executor.getQueue().size());
                // 发送告警、记录日志
                throw new RejectedExecutionException("线程池已满，任务被拒绝");
            }
        });
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(180);
        executor.initialize();
        logger.info("VJ-IO-线程池初始化完成: 核心线程={}, 最大线程={}, 队列容量={}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        return executor;
    }
    /**
     * @description: IO密集型线程池配置
     * <AUTHOR>
     * @date 2025/11/3 14:35
     */
    @Bean
    public ThreadPoolTaskExecutor s7TaskExecutorIoIntensive() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数 = CPU核心数
        int cpuCores = Runtime.getRuntime().availableProcessors();
        // IO密集型可以设置更多线程
        executor.setCorePoolSize(cpuCores * 2);
        executor.setMaxPoolSize(cpuCores * 4);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(120);
        executor.setThreadNamePrefix("S7-IO-Executor-");
        // 拒绝策略：自定义处理
        executor.setRejectedExecutionHandler(new RejectedExecutionHandler() {
            private final Logger logger = LoggerFactory.getLogger(getClass());
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                logger.warn("任务被拒绝，当前活跃线程: {}, 队列大小: {}",
                        executor.getActiveCount(), executor.getQueue().size());
                // 发送告警、记录日志
                throw new RejectedExecutionException("线程池已满，任务被拒绝");
            }
        });
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(180);
        executor.initialize();
        logger.info("S7-IO-线程池初始化完成: 核心线程={}, 最大线程={}, 队列容量={}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        return executor;
    }
}
