package com.llq.hs.service.crawler.VJ.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 2025/07/16
 **/
@Configuration
@Slf4j
public class RestTemplateConfig {


    @Value("${lcc.vj.connectTimeout}")
    private int connectTimeout;
    @Value("${lcc.vj.readTimeout}")
    private int readTimeout;

    /**
     * RestTemplate 统一设置
     *
     * @return
     */
    @Bean(name = "restTemplate")
    public RestTemplate restTemplate() {
        // 1. 配置连接池（最大连接数200，单路由并发20）
        PoolingHttpClientConnectionManager pool = new PoolingHttpClientConnectionManager();
        pool.setMaxTotal(200);
        pool.setDefaultMaxPerRoute(20);
        // 2. 配置超时和重试策略
        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(pool)
                .evictIdleConnections(60, TimeUnit.SECONDS)
                .setDefaultRequestConfig(RequestConfig.custom()
                        .setConnectTimeout(connectTimeout)
                        .setSocketTimeout(readTimeout)
                        .build())
                .build();

        // 3. 使用HttpClient作为底层实现
        return new RestTemplate(new HttpComponentsClientHttpRequestFactory(httpClient));
    }



    /**
     * RestTemplate 统一设置
     *
     * @return
     */
    @Bean(name = "restApiTemplate")
    public RestTemplate restApiTemplate() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        /**
         * 配置信任所有证书
         */
        SSLContext sslContext = new SSLContextBuilder()
                .loadTrustMaterial(null, (TrustStrategy) (chain, authType) -> true)
                .build();
        SSLConnectionSocketFactory sslConnectionSocketFactory =
                new SSLConnectionSocketFactory(sslContext,
                        (hostname, session) -> true);

        // 1. 配置连接池（最大连接数200，单路由并发20）
        PoolingHttpClientConnectionManager pool = new PoolingHttpClientConnectionManager();
        pool.setMaxTotal(200);
        pool.setDefaultMaxPerRoute(20);
        // 2. 配置超时和重试策略
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(sslConnectionSocketFactory)
                .setConnectionManager(pool)
                .evictIdleConnections(60, TimeUnit.SECONDS)
                .setDefaultRequestConfig(RequestConfig.custom()
                        .setConnectTimeout(15000)
                        .setSocketTimeout(30000)
                        .build())
                .build();

        // 3. 使用HttpClient作为底层实现
        RestTemplate  restTemplate = new RestTemplate(new HttpComponentsClientHttpRequestFactory(httpClient));
        // 修改消息转换器
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        return restTemplate;
    }

}
