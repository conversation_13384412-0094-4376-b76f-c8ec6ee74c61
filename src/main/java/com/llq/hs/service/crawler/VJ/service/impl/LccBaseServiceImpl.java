package com.llq.hs.service.crawler.VJ.service.impl;

import com.llq.hs.service.crawler.VJ.service.Lcc_HuilvService;
import lombok.extern.slf4j.Slf4j;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @version 2025/07/10
 **/
@Slf4j
public class LccBaseServiceImpl {

    @Resource
    private Lcc_HuilvService lccHuilvService;

    //保留小数位数
    public static final int SCALE = 2;


    /**
     * 金额汇率转换
     * 保留到分
     * 向上取整
     * @param exchangeRate
     * @param amount
     * @return
     */
    public BigDecimal calcExchangeAmount(BigDecimal exchangeRate, BigDecimal amount){
        BigDecimal exchangeAmount = exchangeRate.multiply(amount);
        return exchangeAmount.setScale(SCALE, RoundingMode.UP);
    }

    public BigDecimal calcExchangeAmount(BigDecimal exchangeRate, BigDecimal amount,int scale){
        BigDecimal exchangeAmount = exchangeRate.multiply(amount);
        return exchangeAmount.setScale(scale, RoundingMode.UP);
    }

    /**
     *
     * @param val
     * @return
     */
    public static BigDecimal transAmount(BigDecimal val){
        if ( null != val ){
            return val.setScale(SCALE, RoundingMode.UP);
        } else {
            return null;
        }
    }

    /**
     * 获取原币种转换到人民币
     *
     * 汇率
     * @param oriCurrency
     * @return
     */
    public BigDecimal getExchangeRate(String oriCurrency){
        return this.getExchangeRate(oriCurrency,Lcc_HuilvService.CNY);
    }


    /**
     * 获取原币种到目的币种汇率
     * @param oriCurrency
     * @param destCurrency
     * @return
     */
    public BigDecimal getExchangeRate(String oriCurrency, String destCurrency){
        BigDecimal exchangeRate = lccHuilvService.getExchangeRate(oriCurrency,destCurrency);
        log.debug("Exchange : ori = {}, dest = {}, rate = {}", oriCurrency,destCurrency,exchangeRate);
        return exchangeRate;
    }
}
