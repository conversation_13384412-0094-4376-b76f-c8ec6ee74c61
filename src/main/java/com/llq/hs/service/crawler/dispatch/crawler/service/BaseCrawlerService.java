package com.llq.hs.service.crawler.dispatch.crawler.service;

import com.llq.hs.service.crawler.dispatch.crawler.dto.HangSiServiceDto;
import com.llq.hs.service.crawler.mq.consumer.MqMessageConfirm;
import com.llq.hs.service.crawler.mq.rep.Lcc_ZhengCe;

/**
 * <AUTHOR>
 * @description: 爬虫服务基础接口
 * @date 2025/11/4
 */
public interface BaseCrawlerService {
    /**
     * 执行爬虫任务
     */
    Lcc_ZhengCe executeCrawler(HangSiServiceDto message, String hangSi, MqMessageConfirm mqMessageConfirm);


    /**
     * 获取支持的航司代码
     */
    String getSupportedAirline();

    /**
     * 获取爬虫服务名称
     */
    String getCrawlerName();
}