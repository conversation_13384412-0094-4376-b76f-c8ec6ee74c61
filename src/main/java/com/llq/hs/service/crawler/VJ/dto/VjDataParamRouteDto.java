package com.llq.hs.service.crawler.VJ.dto;

/**
 * <AUTHOR>
 * @version 2025/07/07
 **/
public class VjDataParamRouteDto {


    /**
     *
     * 出发时间
     */
    private String departureDate;

    /**
     *
     * 目的地
     *
     */
    private String destination;

    /**
     * 出发地
     */
    private String origin;


    public String getDepartureDate() {
        return departureDate;
    }

    public void setDepartureDate(String departureDate) {
        this.departureDate = departureDate;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    @Override
    public String toString() {
        return "routes{" +
                "departureDate='" + departureDate + '\'' +
                ", destination='" + destination + '\'' +
                ", origin='" + origin + '\'' +
                '}';
    }
}
