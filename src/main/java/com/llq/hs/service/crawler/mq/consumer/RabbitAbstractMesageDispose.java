package com.llq.hs.service.crawler.mq.consumer;


import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;

/**
 * <AUTHOR>
 * @description: Mq消息确认抽象类
 * @date 2025/11/5 12:05
 */
public abstract  class RabbitAbstractMesageDispose {
    private static final Logger logger = LoggerFactory.getLogger(RabbitAbstractMesageDispose.class);
    /**
     * @description: 消息拒绝并丢弃
     * <AUTHOR>
     * @date 2025/11/5 11:22
     */
    public void basicNack(Channel channel, Message amqpMessage, String hangSi){
        try {
            channel.basicNack(amqpMessage.getMessageProperties().getDeliveryTag(), false, false);
        } catch (Exception e) {
            logger.error("拒绝{}消息失败: {}", hangSi,e.getMessage());
        }
    }
    /**
     * @description: 消息拒绝并重新放入队列
     * <AUTHOR>
     * @date 2025/11/5 11:22
     */
    public void basicNackRetrun(Channel channel,Message amqpMessage,String hangSi){
        try {
            channel.basicNack(amqpMessage.getMessageProperties().getDeliveryTag(), false, true);
        } catch (Exception e) {
            logger.error("拒绝{}消息并重新放入队列失败: {}", hangSi,e.getMessage());
        }
    }
    /**
     * @description: 手动消息确认
     * <AUTHOR>
     * @date 2025/11/5 11:22
     */
    public void confirmManuallyMessage(Channel channel,Message amqpMessage,String hangSi){
        try {
            channel.basicAck(amqpMessage.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            logger.error("手动确认{}消息失败: {}", hangSi,e.getMessage());
        }
    }
}
