package com.llq.hs.service.crawler.common.utils;

import com.llq.hs.service.crawler.common.constanst.RequestConst;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @description: 异步HTTP请求工具类
 * 特点：HTTP请求层面：异步（非阻塞IO）
 *（1）HTTP层面不阻塞IO线程
 * （2）业务层可以精确控制超时时间
 * （3）同步编程，问题较为容易定位和控制
 * 业务层面：同步等待结果（通过CompletableFuture.get()），可针对超时时间进行自定义
 * 用法：需要使用者根据实际业务场景来自定义请求超时的设置
 * 缺陷：（1）线程占用：在等待期间会占用线程池线程
 *      （2）并发受限：受限于线程池大小
 *      （3）无法实时响应结果
 * @date 2025/11/5
 */
@Component
public class AsyncHttpClient {

    private static final Logger logger = LoggerFactory.getLogger(AsyncHttpClient.class);

    private final WebClient webClient;
    private final AtomicLong requestCounter = new AtomicLong(0);
    private final Map<String, RequestStats> requestStats = new ConcurrentHashMap<>();
    //默认超时-秒
    private static final int TIMEOUT=30;

    // 请求统计信息
    private static class RequestStats {
        long totalRequests;
        long successRequests;
        long failedRequests;
        long totalResponseTime;
    }

    // 请求配置
    public static class RequestConfig {
        private Duration timeout = Duration.ofSeconds(TIMEOUT);
        private int maxRetries = 0;
        private Map<String, String> headers;
        private Map<String, String> queryParams;

        // Builder模式
        public static RequestConfig builder() {
            return new RequestConfig();
        }

        public RequestConfig timeout(Duration timeout) {
            this.timeout = timeout;
            return this;
        }

        public RequestConfig maxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }

        public RequestConfig headers(Map<String, String> headers) {
            this.headers = headers;
            return this;
        }

        public RequestConfig queryParams(Map<String, String> queryParams) {
            this.queryParams = queryParams;
            return this;
        }

        public RequestConfig build() {
            return this;
        }

        // Getter方法
        public Duration getTimeout() { return timeout; }
        public int getMaxRetries() { return maxRetries; }
        public Map<String, String> getHeaders() { return headers; }
        public Map<String, String> getQueryParams() { return queryParams; }
    }

    // 响应结果
    public static class HttpResponse {
        private final String requestId;
        private final boolean success;
        private final int statusCode;
        private final String body;
        private final String errorMessage;
        private final long responseTime;
        private final Map<String, String> headers;

        public HttpResponse(String requestId, boolean success, int statusCode, String body,
                            String errorMessage, long responseTime, Map<String, String> headers) {
            this.requestId = requestId;
            this.success = success;
            this.statusCode = statusCode;
            this.body = body;
            this.errorMessage = errorMessage;
            this.responseTime = responseTime;
            this.headers = headers;
        }

        // Getters
        public String getRequestId() { return requestId; }
        public boolean isSuccess() { return success; }
        public int getStatusCode() { return statusCode; }
        public String getBody() { return body; }
        public String getErrorMessage() { return errorMessage; }
        public long getResponseTime() { return responseTime; }
        public Map<String, String> getHeaders() { return headers; }

        @Override
        public String toString() {
            return String.format("HttpResponse{requestId='%s', success=%s, statusCode=%d, responseTime=%dms}",
                    requestId, success, statusCode, responseTime);
        }
    }

    public AsyncHttpClient() {
        this.webClient = WebClient.builder()
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.USER_AGENT, "Crawler-Async-HTTP-Client/1.0")
                .build();
    }

    public AsyncHttpClient(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.build();
    }

    /**
     * 异步GET请求
     */
    @Async("taskExecutor")
    public CompletableFuture<HttpResponse> getAsync(String url, RequestConfig config) {
        String requestId = generateRequestId(HttpMethod.GET.name(), url);
        long startTime = System.currentTimeMillis();

        logger.info(" [{}] 开始异步{}请求: {}", requestId,HttpMethod.GET.name(), url);

        // 构建URI
        URI uri = buildUri(url, config);

        return webClient.get()
                .uri(uri)
                .headers(headers -> addCustomHeaders(headers, config))
                .retrieve()
                .toEntity(String.class)
                .timeout(config.getTimeout())
                .map(responseEntity -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    HttpResponse response = new HttpResponse(
                            requestId, true, responseEntity.getStatusCodeValue(),
                            responseEntity.getBody(), null, responseTime,
                            extractHeaders(responseEntity.getHeaders())
                    );
                    recordRequestStats(true, responseTime);
                    logger.info(" [{}] {}请求成功, 状态码: {}, 耗时: {}ms",
                            requestId, HttpMethod.GET.name(),responseEntity.getStatusCodeValue(), responseTime);
                    return response;
                })
                .onErrorResume(throwable -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    HttpResponse errorResponse = handleError(requestId, throwable, responseTime);
                    recordRequestStats(false, responseTime);
                    return Mono.just(errorResponse);
                })
                .toFuture();
    }

    /**
     * 异步POST请求
     */
    @Async("taskExecutor")
    public CompletableFuture<HttpResponse> postAsync(String url, Object body, RequestConfig config) {
        String requestId = generateRequestId(HttpMethod.POST.name(), url);
        long startTime = System.currentTimeMillis();

        logger.info(" [{}] 开始异步{}请求: {}", requestId,HttpMethod.POST.name(), url);

        // 构建URI
        URI uri = buildUri(url, config);

        return webClient.post()
                .uri(uri)
                .headers(headers -> addCustomHeaders(headers, config))
                .bodyValue(body)
                .retrieve()
                .toEntity(String.class)
                .timeout(config.getTimeout())
                .map(responseEntity -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    HttpResponse response = new HttpResponse(
                            requestId, true, responseEntity.getStatusCodeValue(),
                            responseEntity.getBody(), null, responseTime,
                            extractHeaders(responseEntity.getHeaders())
                    );
                    recordRequestStats(true, responseTime);
                    logger.info(" [{}] {}请求成功, 状态码: {}, 耗时: {}ms",
                            requestId,HttpMethod.POST.name(), responseEntity.getStatusCodeValue(), responseTime);
                    return response;
                })
                .onErrorResume(throwable -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    HttpResponse errorResponse = handleError(requestId, throwable, responseTime);
                    recordRequestStats(false, responseTime);
                    return Mono.just(errorResponse);
                })
                .toFuture();
    }

    /**
     * 异步PUT请求
     */
    @Async("taskExecutor")
    public CompletableFuture<HttpResponse> putAsync(String url, Object body, RequestConfig config) {
        String requestId = generateRequestId(HttpMethod.PUT.name(), url);
        long startTime = System.currentTimeMillis();

        logger.info(" [{}] 开始异步{}请求: {}", requestId,HttpMethod.PUT.name(), url);

        // 构建URI
        URI uri = buildUri(url, config);

        return webClient.put()
                .uri(uri)
                .headers(headers -> addCustomHeaders(headers, config))
                .bodyValue(body)
                .retrieve()
                .toEntity(String.class)
                .timeout(config.getTimeout())
                .map(responseEntity -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    HttpResponse response = new HttpResponse(
                            requestId, true, responseEntity.getStatusCodeValue(),
                            responseEntity.getBody(), null, responseTime,
                            extractHeaders(responseEntity.getHeaders())
                    );
                    recordRequestStats(true, responseTime);
                    logger.info(" [{}] {}请求成功, 状态码: {}, 耗时: {}ms",
                            requestId, HttpMethod.PUT.name(),responseEntity.getStatusCodeValue(), responseTime);
                    return response;
                })
                .onErrorResume(throwable -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    HttpResponse errorResponse = handleError(requestId, throwable, responseTime);
                    recordRequestStats(false, responseTime);
                    return Mono.just(errorResponse);
                })
                .toFuture();
    }

    /**
     * 异步DELETE请求
     */
    @Async("taskExecutor")
    public CompletableFuture<HttpResponse> deleteAsync(String url, RequestConfig config) {
        String requestId = generateRequestId(HttpMethod.DELETE.name(), url);
        long startTime = System.currentTimeMillis();

        logger.info(" [{}] 开始异步{}请求: {}", requestId, HttpMethod.DELETE.name(),url);

        // 构建URI
        URI uri = buildUri(url, config);

        return webClient.delete()
                .uri(uri)
                .headers(headers -> addCustomHeaders(headers, config))
                .retrieve()
                .toEntity(String.class)
                .timeout(config.getTimeout())
                .map(responseEntity -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    HttpResponse response = new HttpResponse(
                            requestId, true, responseEntity.getStatusCodeValue(),
                            responseEntity.getBody(), null, responseTime,
                            extractHeaders(responseEntity.getHeaders())
                    );
                    recordRequestStats(true, responseTime);
                    logger.info(" [{}] {}请求成功, 状态码: {}, 耗时: {}ms",
                            requestId, HttpMethod.DELETE.name(),responseEntity.getStatusCodeValue(), responseTime);
                    return response;
                })
                .onErrorResume(throwable -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    HttpResponse errorResponse = handleError(requestId, throwable, responseTime);
                    recordRequestStats(false, responseTime);
                    return Mono.just(errorResponse);
                })
                .toFuture();
    }

    // 私有辅助方法
    private URI buildUri(String url, RequestConfig config) {
        // 使用 Spring 的 UriComponentsBuilder 来构建 URI
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(url);

        // 添加查询参数
        if (config.getQueryParams() != null) {
            config.getQueryParams().forEach(uriBuilder::queryParam);
        }

        return uriBuilder.build().toUri();
    }

    private void addCustomHeaders(HttpHeaders headers, RequestConfig config) {
        if (config.getHeaders() != null) {
            config.getHeaders().forEach(headers::add);
        }
    }

    private Map<String, String> extractHeaders(org.springframework.http.HttpHeaders headers) {
        Map<String, String> result = new java.util.HashMap<>();
        headers.forEach((key, values) -> result.put(key, String.join(", ", values)));
        return result;
    }

    private HttpResponse handleError(String requestId, Throwable throwable, long responseTime) {
        String errorMessage;
        int statusCode = 0;

        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            statusCode = ex.getStatusCode().value();
            errorMessage = String.format("HTTP错误: %d - %s", statusCode, ex.getStatusText());
            logger.error(" [{}] HTTP请求失败: {}", requestId, errorMessage);
        } else if (throwable instanceof java.util.concurrent.TimeoutException) {
            errorMessage = "请求超时";
            logger.error(" [{}] 请求超时", requestId);
        } else {
            errorMessage = throwable.getMessage();
            logger.error(" [{}] 请求异常: {}", requestId, errorMessage, throwable);
        }

        return new HttpResponse(requestId, false, statusCode, null, errorMessage, responseTime, null);
    }

    private String generateRequestId(String method, String url) {
        return String.format("REQ-%s-%d-%d",
                method, System.currentTimeMillis(), requestCounter.incrementAndGet());
    }

    private void recordRequestStats(boolean success, long responseTime) {
        String statsKey = "global";
        RequestStats stats = requestStats.computeIfAbsent(statsKey, k -> new RequestStats());

        stats.totalRequests++;
        stats.totalResponseTime += responseTime;

        if (success) {
            stats.successRequests++;
        } else {
            stats.failedRequests++;
        }
    }

    /**
     * 获取请求统计信息
     */
    public Map<String, Object> getRequestStats() {
        RequestStats stats = requestStats.get("global");
        if (stats == null) {
            return Map.of();
        }

        long avgResponseTime = stats.totalRequests > 0 ? stats.totalResponseTime / stats.totalRequests : 0;

        return Map.of(
                "totalRequests", stats.totalRequests,
                "successRequests", stats.successRequests,
                "failedRequests", stats.failedRequests,
                "successRate", stats.totalRequests > 0 ?
                        (double) stats.successRequests / stats.totalRequests : 0,
                "averageResponseTime", avgResponseTime
        );
    }
    /**
     * 使用示例
     */
    public static void main(String[] args) throws Exception {
        // 创建客户端实例
        AsyncHttpClient client = new AsyncHttpClient();

        // 配置请求参数
        RequestConfig config = RequestConfig.builder()
                .timeout(Duration.ofSeconds(10))
                .headers(Map.of(HttpHeaders.USER_AGENT, "Test-Client/1.0"))
                .queryParams(Map.of("key", "value"))
                .build();

        // 发起异步GET请求
        CompletableFuture<HttpResponse> future = client.getAsync("https://httpbin.org/get", config);

        // 等待并获取结果
        HttpResponse response = future.get();

        if (response.isSuccess()) {
            System.out.println("请求成功: " + response.getStatusCode());
            System.out.println("响应内容: " + response.getBody().substring(0, 100) + "...");
        } else {
            System.out.println("请求失败: " + response.getErrorMessage());
        }

        // 查看统计信息
        System.out.println("请求统计: " + client.getRequestStats());
    }
}