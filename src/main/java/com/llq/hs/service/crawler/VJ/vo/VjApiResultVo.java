package com.llq.hs.service.crawler.VJ.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 2025/07/08
 **/
@Setter
@Getter
@ToString
public class VjApiResultVo {

    /**
     * 200
     */
    private boolean success;

    /**
     * Success
     */
    private String msg;

    public Object data;

    public static VjApiResultVo ok(Object data){
        VjApiResultVo resultVo = new VjApiResultVo();
        resultVo.setSuccess(true);
        resultVo.setData(data);
        return resultVo;
    }

    public static VjApiResultVo fail(String msg){
        VjApiResultVo resultVo = new VjApiResultVo();
        resultVo.setSuccess(false);
        resultVo.setMsg(msg);
        return resultVo;
    }





}
