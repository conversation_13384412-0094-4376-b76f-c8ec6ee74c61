package com.llq.hs.service.crawler.VJ.service;


import com.llq.hs.service.crawler.VJ.dto.VjResultDataDto;
import com.llq.hs.service.crawler.VJ.entity.Lcc_ZhengCe;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/07/08
 **/
public interface Lcc_VjSaveDataService {


    /**
     * 保存航班数据
     * @param dataDtoList
     * @param logId
     */
    void saveOneWay(List<VjResultDataDto> dataDtoList, Long logId) ;

    List<Lcc_ZhengCe> getOneWay(List<VjResultDataDto> dataDtoList);
}
