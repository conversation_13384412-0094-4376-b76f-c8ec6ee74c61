package com.llq.hs.service.crawler.mq.consumer;


import cn.hutool.core.util.ObjectUtil;
import com.llq.hs.service.crawler.dispatch.hangsi.HangSiDispatchCenter;
import com.llq.hs.service.crawler.mq.req.SaasSysRequestMessage;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;



/**
 * @description: 消费者服务
 * <AUTHOR>
 * @date 2025/11/3 11:44
 */
@Component
public class MessageConsumer {
    private static final Logger logger = LoggerFactory.getLogger(MessageConsumer.class);

    @Autowired
    private HangSiDispatchCenter disPatchCenter;

    // 监听指定队列 concurrency:以客户端支撑多个消费者异步消费消息
    @RabbitListener(queues = "${rabbitConfig.consumer.queueName}")
    public void handleMessage(@Payload SaasSysRequestMessage message, Channel channel, Message amqpMessage) throws IOException {
        try {
            // 获取指定队列中的消息
            logger.info("处理消息内容: {}", message);
            if (ObjectUtil.isNotEmpty(message)) {
                disPatchCenter.dispatch(message, channel, amqpMessage);
            } else {
                logger.info("Message is empty or null");
            }
        } catch (Exception e) {
            logger.error("消息处理失败: {}", e.getMessage());
        }
    }

}
