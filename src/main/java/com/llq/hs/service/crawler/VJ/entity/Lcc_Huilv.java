package com.llq.hs.service.crawler.VJ.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName lcc_huiLv
 */
@TableName(value ="lcc_huiLv")
@Data
public class Lcc_Huilv implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 币种三字码
     */
    @TableField(value = "biZhongCode")
    private String biZhongCode;

    /**
     * 币种名称
     */
    @TableField(value = "biZhongName")
    private String biZhongName;

    /**
     * 汇率
     */
    @TableField(value = "huiLv")
    private BigDecimal huiLv;

    /**
     * 更新时间
     */
    @TableField(value = "updateTime")
    private Date updateTime;

    /**
     * 日志ID
     */
    @TableField(value = "logId")
    private Long logId;

    /**
     * 日志类型,1为添加，0为修改，-1为删除
     */
    @TableField(value = "logType")
    private Integer logType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}