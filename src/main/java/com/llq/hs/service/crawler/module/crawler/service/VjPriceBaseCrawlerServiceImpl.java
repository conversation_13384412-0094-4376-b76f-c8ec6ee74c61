package com.llq.hs.service.crawler.module.crawler.service;


import com.llq.hs.service.crawler.common.constanst.CrawlerConst;
import com.llq.hs.service.crawler.common.constanst.HangSiConst;
import com.llq.hs.service.crawler.common.constanst.RequestConst;
import com.llq.hs.service.crawler.dispatch.crawler.dto.HangSiServiceDto;
import com.llq.hs.service.crawler.dispatch.crawler.service.BaseCrawlerService;
import com.llq.hs.service.crawler.module.crawler.util.http.AsyncHttpClientWithMQ;
import com.llq.hs.service.crawler.mq.consumer.MqMessageConfirm;
import com.llq.hs.service.crawler.mq.rep.Lcc_ZhengCe;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: VJ航司票价爬虫服务-样例
 * @date 2025/11/4
 */
@Service
public class VjPriceBaseCrawlerServiceImpl implements BaseCrawlerService {
    private static final Logger logger = LoggerFactory.getLogger(VjPriceBaseCrawlerServiceImpl.class);
    @Autowired
    private AsyncHttpClientWithMQ asyncHttpClient;
    @Override
    public Lcc_ZhengCe executeCrawler(HangSiServiceDto message, String hangSi, MqMessageConfirm mqMessageConfirm) {
        logger.info("执行{}航司票价爬虫任务: {}", hangSi,message);

        // 具体的VJ爬虫逻辑
        try {
            // 构建请求配置
            AsyncHttpClientWithMQ.RequestConfig config = AsyncHttpClientWithMQ.RequestConfig.builder()
                    .timeout(Duration.ofSeconds(RequestConst.TIME_OUT))
                    .airline(HangSiConst.HANGSI_S7)
                    .taskType("")
                    .headers(Map.of(
                            HttpHeaders.USER_AGENT, "测试2/1.0",
                            HttpHeaders.ACCEPT,  MediaType.APPLICATION_JSON_VALUE,
                            RequestConst.X_AIRLINE, "测试2"
                    ))
                    .queryParams(buildVjQueryParams(message))
                    .build();

            // 使用异步HTTP客户端执行爬虫请求
            AsyncHttpClientWithMQ.AsyncRequestResult asyncResult =
                    asyncHttpClient.executeCrawlerAsync(CrawlerConst.VJ_CRAWLER_URL, config,mqMessageConfirm);

            logger.info("{}航司异步爬虫任务已启动, 请求ID: {}", hangSi, asyncResult.getRequestId());


        } catch (Exception e) {
            logger.error("{}票价爬虫执行异常: {}", hangSi, e.getMessage(), e);
        }
        return null;
    }

    private Map<String, String> buildVjQueryParams(HangSiServiceDto message) {
        return Map.of(
                "hangSi", message.getDepartureDate() != null ? message.getDepartureDate() : "",
                "departureDate", message.getHangSi() != null ? message.getHangSi() : ""
        );
    }
    @Override
    public String getSupportedAirline() {
        return HangSiConst.HANGSI_VJ;
    }

    @Override
    public String getCrawlerName() {
        return "";
    }
}