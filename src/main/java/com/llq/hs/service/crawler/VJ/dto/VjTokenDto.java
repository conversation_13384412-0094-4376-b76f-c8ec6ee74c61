package com.llq.hs.service.crawler.VJ.dto;


import com.llq.hs.service.crawler.VJ.config.VjApiConfig;
import com.llq.hs.service.crawler.VJ.enums.AirLineType;

/**
 * <AUTHOR>
 * @version 2025/07/07
 **/
public class VjTokenDto {

    private String air_type = AirLineType.VJ.getCode();
    private String token = VjApiConfig.token;
    private VjTokenDataDto data;

    public String getAir_type() {
        return air_type;
    }

    public void setAir_type(String air_type) {
        this.air_type = air_type;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public VjTokenDataDto getData() {
        return data;
    }

    public void setData(VjTokenDataDto data) {
        this.data = data;
    }


    @Override
    public String toString() {
        return "VjTokenDto{" +
                "air_type='" + air_type + '\'' +
                ", token='" + token + '\'' +
                ", data=" + data +
                '}';
    }
}
