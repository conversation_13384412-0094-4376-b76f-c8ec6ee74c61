package com.llq.hs.service.crawler.VJ.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025/07/10
 **/
@Data
public class VjResultDataDto {


    /**
     * 政策出发城市三字码
     */
    private String depCity;

    /**
     * 政策目的城市三字码
     */
    private String arrCity;



    /**
     * 去程日期 yyyy_MM_dd,投的都是单日期政策
     */
    private String dateStr_go;

    /**
     * 回程日期 yyyy_MM_dd,投的都是单日期政策
     * 构造往返政策时必传，单程不能传值，需要根据它来判断是否为往返政策
     */
    private String dateStr_back;


    /**
     * 基础价格
     */
    private VjResultPriceDataDto basicPriceDto;


    /**
     * 标准价格
     */
    private VjResultPriceDataDto standardPriceDto;






}
