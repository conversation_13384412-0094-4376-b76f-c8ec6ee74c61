package com.llq.hs.service.crawler.VJ.controller;

import com.google.common.base.Strings;
import com.llq.hs.service.crawler.VJ.entity.Lcc_Huilv;
import com.llq.hs.service.crawler.VJ.service.Lcc_HuilvService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2025/08/12
 **/
@RestController
public class VjExchangeController {


    @Resource
    private Lcc_HuilvService lccHuilvService;

    /**
     *
     * 手动更新汇率
     * @param ori
     * @throws IOException
     */
    @RequestMapping(value = "admin.huilv.do")
    public void handleHuilv(String ori, String rate,HttpServletResponse response) throws IOException {
        //如果为空直接返回
        if (Strings.isNullOrEmpty(ori) || Strings.isNullOrEmpty(rate)){
            // 设置响应头
            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().write("原币种[ori]或者汇率[rate]为空");
            return;
        }
        BigDecimal value = new BigDecimal(rate.trim());
        Lcc_Huilv lccHuilv = lccHuilvService.setExchangeRate(ori.trim(),value);
        // 设置响应头
        response.setContentType("text/html;charset=UTF-8");
        response.getWriter().write(null != lccHuilv ?
                ("汇率缓存：" + lccHuilv.getBiZhongCode() + "-" + lccHuilv.getHuiLv())
                : "未查询到币种");
    }

}
