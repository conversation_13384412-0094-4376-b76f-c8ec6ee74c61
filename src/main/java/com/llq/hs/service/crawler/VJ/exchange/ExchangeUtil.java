package com.llq.hs.service.crawler.VJ.exchange;

import com.llq.hs.service.crawler.VJ.exchange.dto.ExChangeDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 功能描述
 *
 *
 * @author: kongwy
 * @date: 2025年07月13日 16:36
 */
@Slf4j
public class ExchangeUtil {

    public static final String URL = "http://api.k780.com";
    public static final String AppKey = "76380";
    public static final String Sign="3dab1611295874e644162cc8f1e46451";



    /**
     *
     * 支持一对多，多对多
     * 不建议一对一使用
     * 除非未配置不能定时获取
     * 获取汇率
     * @param ori
     * @return
     */
    public  BigDecimal getExchangeRate2CNY(String ori){
        return getExchangeRate(ori, "CNY");
    }


    /**
     * 获取汇率
     * @param ori
     * @param dest
     * @return
     */
    public  BigDecimal getExchangeRate(String ori, String dest){
        log.info("ExchangeUtil-getExchangeRate:ori:{} - dest:{}",ori,dest);
        // https://sapi.k780.com/?app=finance.rate&scur=USD&tcur=CNY&appkey=10003&sign=b59bc3ef6191eb9f747dd4e83c99f2a4
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> param = new LinkedMultiValueMap<String,String>();
        param.add("app", "finance.rate");
        param.add("scur", ori);
        param.add("tcur", "CNY");
        param.add("format", "json");
        param.add("appkey", AppKey);
        param.add("sign", Sign);
        HttpEntity<MultiValueMap<String, String> > entity  = new HttpEntity<>(param,headers);
        ExChangeDto result = restTemplate.postForObject(URL,entity, ExChangeDto.class);
        if (result.ok()){
            return new BigDecimal(result.getResult().getRate()).setScale(6, RoundingMode.HALF_UP);
        }else {
            throw new RuntimeException("ExchangeUtil-getExchangeRate-get-error:" +ori);
        }
    }

    public static void main(String[] args) {
        //System.out.println(new ExchangeUtil().getExchangeRate("RUB","CNY"));
    }

}
