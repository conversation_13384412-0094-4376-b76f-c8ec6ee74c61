package com.llq.hs.service.crawler.VJ.config;

import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * vj接口地址
 *
 * @author: kongwy
 * @date: 2025年07月12日 23:48
 */
@Configuration
@Slf4j
public class VjApiConfig {


    public static String url;

    public static String token;

    public static int connectTimeout;

    public static int readTimeout;

    //链接池构建
    private static List<String> urls = null;
    private static int poolSize;


    @Value("${lcc.vj.url}")
    public void setUrl(String url) {
        VjApiConfig.url = url;
        VjApiConfig.urls = Splitter.on(";").omitEmptyStrings()
                .trimResults()
                .splitToList(url);
        if ( null == VjApiConfig.urls || VjApiConfig.urls.isEmpty()){
            log.error("VJApiConfig-setUrl-{}", VjApiConfig.urls);
            throw new RuntimeException("未配置接口请求地址");
        }
        poolSize = VjApiConfig.urls.size();
    }

    @Value("${lcc.vj.token}")
    public void setToken(String token) {
        VjApiConfig.token = token;
    }

    @Value("${lcc.vj.connectTimeout}")
    public  void setConnectTimeout(int connectTimeout) {
        VjApiConfig.connectTimeout = connectTimeout;
    }

    @Value("${lcc.vj.readTimeout}")
    public  void setReadTimeout(int readTimeout) {
        VjApiConfig.readTimeout = readTimeout;
    }

    @PostConstruct
    public void loadConfig(){
        log.info("load vj gather config finish！！");
    }


    /**
     * 根据序号获取链接池地址
     *
     * 序号%链接池数量
     *  4%3 = 1
     *  0%3 = 0
     *  3%3 = 0
     * 目前0-2
     *
     * @param seq
     * @return
     */
    public static String getPoolUrl(int seq) {
        int idx = getPoolIdx(seq);
        return VjApiConfig.urls.get(idx);
    }

    /**
     * 获取池索引号
     * @param seq
     * @return
     */
    public static int getPoolIdx(int seq) {
        int idx = seq % poolSize;
        return idx;
    }
}
