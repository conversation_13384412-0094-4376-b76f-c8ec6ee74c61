package com.llq.hs.service.crawler.config.mq;


import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Description:Mq配置类
 * <AUTHOR>
 */
@Configuration
public class RabbitConfig {
    //消费者队列名称
    @Value("${rabbitConfig.consumer.queueName}")
    private String consumerQueueName;
    //交换机名称
    @Value("${rabbitConfig.exchangeName}")
    private  String exchangeName;
    //生产者队列名称
    @Value("${rabbitConfig.producer.queueName}")
    private  String producerQueueName;
    @Value("${rabbitConfig.producer.routingKey}")
    private String producerRoutingKey;


    //爬虫结果生产者队列名称
    @Value("${rabbitConfig.producerRep.queueName}")
    private  String producerRepQueueName;
    @Value("${rabbitConfig.producerRep.routingKey}")
    private String producerRepRoutingKey;

    // 消息转换器 - 使用JSON序列化
    @Bean
    public Jackson2JsonMessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }


    // 声明交换机
    @Bean
    public DirectExchange directExchange() {
        // 参数：交换机名称、是否持久化、是否自动删除、其他属性
        return new DirectExchange(exchangeName, true, false);
    }
    // 声明队列
    @Bean
    public Queue demoQueue() {
        // 参数：队列名称、是否持久化、是否排他、是否自动删除、其他属性
        return QueueBuilder.durable(producerQueueName)
                // 消息过期时间180秒
                .withArgument("x-message-ttl", 180000)
                .build();
    }
    // 绑定交换机和队列
    @Bean
    public Binding binding() {
        return BindingBuilder.bind(demoQueue())
                .to(directExchange())
                .with(producerRoutingKey);
    }

    // 声明队列
    @Bean
    public Queue repQueue() {
        // 参数：队列名称、是否持久化、是否排他、是否自动删除、其他属性
        return QueueBuilder.durable(producerRepQueueName)
                // 消息过期时间180秒
                .withArgument("x-message-ttl", 180000)
                .build();
    }
    // 绑定交换机和队列
    @Bean
    public Binding repBinding() {
        return BindingBuilder.bind(repQueue())
                .to(directExchange())
                .with(producerRepRoutingKey);
    }



    // 配置RabbitTemplate
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(messageConverter());

        // 消息确认回调
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                System.out.println("消息成功发送到交换机");
            } else {
                System.out.println("消息发送到交换机失败: " + cause);
            }
        });

        // 消息返回回调
        rabbitTemplate.setReturnsCallback(returnedMessage -> {
            System.out.println("消息未送达队列: " + returnedMessage.getReplyText());
        });

        return rabbitTemplate;
    }
}
