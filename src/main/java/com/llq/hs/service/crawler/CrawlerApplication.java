package com.llq.hs.service.crawler;

import com.llq.hs.service.crawler.mq.producer.MessageService;
import com.llq.hs.service.crawler.mq.req.SaasSysRequestMessage;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;


import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;




/**
 * <AUTHOR>
 */
@MapperScan(value = CrawlerApplication.COMPONENT_SCAN, annotationClass = Mapper.class)
@SpringBootApplication
@EnableScheduling
@EnableAsync
public class CrawlerApplication {
    @Autowired
    private MessageService messageService;
    public static final String COMPONENT_SCAN = "com.llq.hs.service.crawler";

    public static void main(String[] args) {
        SpringApplication.run(CrawlerApplication.class, args);
    }

//    @Scheduled(fixedRate = 500)  // 5000毫秒 = 5秒
//    public void sendTestMessages() {
//        for (int i = 0; i < 4; i++) {
//            SaasSysRequestMessage saasSysRequestMessageVj = new SaasSysRequestMessage();
//            saasSysRequestMessageVj.setHangSi("VJ");
//            saasSysRequestMessageVj.setDepCity("出发城市三字码");
//            saasSysRequestMessageVj.setArrCity("到达城市三字码");
//            saasSysRequestMessageVj.setDepartureDate("出发日期");
//            saasSysRequestMessageVj.setCurrency("货币类型");
//            saasSysRequestMessageVj.setDepAirport("出发机场三字码");
//            saasSysRequestMessageVj.setArrAirport("到达机场三字码");
//            messageService.sendMessage(saasSysRequestMessageVj);
//
//        SaasSysRequestMessage saasSysRequestMessageS7 = new SaasSysRequestMessage();
//        saasSysRequestMessageS7.setHangSi("S7");
//        saasSysRequestMessageS7.setDepCity("出发城市三字码");
//        saasSysRequestMessageS7.setArrCity("到达城市三字码");
//        saasSysRequestMessageS7.setDepartureDate("出发日期");
//        saasSysRequestMessageS7.setCurrency("货币类型");
//        saasSysRequestMessageS7.setDepAirport("出发机场三字码");
//        saasSysRequestMessageS7.setArrAirport("到达机场三字码");
//            messageService.sendMessage(saasSysRequestMessageS7);
//        }
//    }



}
