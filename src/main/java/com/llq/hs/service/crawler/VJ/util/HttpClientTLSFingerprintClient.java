package com.llq.hs.service.crawler.VJ.util;

import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.DefaultHostnameVerifier;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import java.io.IOException;
import java.util.concurrent.CompletableFuture;

public class HttpClientTLSFingerprintClient {
    private static final Logger logger = LoggerFactory.getLogger(HttpClientTLSFingerprintClient.class);

    public static String fingerprint = "chrome_106";
    private final CloseableHttpClient httpClient;

    public HttpClientTLSFingerprintClient(String fingerprint) {
        this.fingerprint = fingerprint;
        this.httpClient = createHttpClient("",0,"","");
    }

    /**
     * 发送HTTP GET请求
     *
     * @param url 目标URL
     * @return 响应内容
     */
    public CompletableFuture<String> get(String url) {
        return sendRequest(new HttpGet(url));
    }

    /**
     * 发送HTTP POST请求
     *
     * @param url     目标URL
     * @param content 请求内容
     * @return 响应内容
     */
    public CompletableFuture<String> post(String url, String content) {
        HttpPost post = new HttpPost(url);
        if (content != null) {
            post.setEntity(new StringEntity(content, "UTF-8"));
        }
        return sendRequest(post);
    }

    /**
     * 发送HTTP请求
     *
     * @param request HTTP请求
     * @return 响应内容
     */
    private CompletableFuture<String> sendRequest(HttpUriRequest request) {
        CompletableFuture<String> future = new CompletableFuture<>();

        // 设置请求头
        request.setHeader("User-Agent", getUserAgent());
        request.setHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
        request.setHeader("Accept-Language", "en-US,en;q=0.5");
        request.setHeader("Accept-Encoding", "gzip, deflate");
        request.setHeader("Connection", "close");

        new Thread(() -> {
            try {
                logger.info("发送{}请求到: {}", request.getMethod(), request.getURI());
                HttpResponse response = httpClient.execute(request);
                HttpEntity entity = response.getEntity();
                String responseBody = entity != null ? EntityUtils.toString(entity) : "";
                future.complete(responseBody);
            } catch (Exception e) {
                logger.error("发送请求时出错", e);
                future.completeExceptionally(e);
            }
        }).start();

        return future;
    }

    /**
     * 创建支持TLS指纹的HTTP客户端
     *
     * @return CloseableHttpClient
     */
    public static CloseableHttpClient createHttpClient(String proxy_host,int proxy_post,String proxy_user,String proxy_pass) {
        try {

            HttpHost proxy = new HttpHost(proxy_host, proxy_post, "http");

            // 创建自定义SSL上下文
            SSLContext sslContext = SSLContexts.createDefault();

            // 创建自定义SSL连接工厂
            SSLConnectionSocketFactory sslSocketFactory = new CustomSSLConnectionSocketFactory(
                    sslContext,
                    getProtocolsForFingerprint(),
                    getCipherSuitesForFingerprint()
            );

            // 配置请求参数
            RequestConfig requestConfig = RequestConfig.custom()
                    .setProxy(proxy)
                    .setConnectTimeout(10000)
                    .setSocketTimeout(15000)
                    .setConnectionRequestTimeout(5000)
                    .build();

            PoolingHttpClientConnectionManager connectionManager =
                    new PoolingHttpClientConnectionManager();
            connectionManager.setMaxTotal(50);
            connectionManager.setDefaultMaxPerRoute(10);

            // 认证配置
            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(
                    new AuthScope(proxy_host, proxy_post),
                    new UsernamePasswordCredentials(proxy_user, proxy_pass)
            );

            // SSL配置（如果需要）
            SSLContext sslContext1 = SSLContexts.custom()
                    .loadTrustMaterial(null, (chain, authType) -> true)
                    .build();

            // 创建HTTP客户端
            return HttpClients.custom()
                    .setConnectionManager(connectionManager)
                    .setSSLSocketFactory(sslSocketFactory)
                    .setDefaultCredentialsProvider(credentialsProvider)
                    .setDefaultRequestConfig(requestConfig)
//                    .setSSLContext(sslContext1)
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .setRetryHandler(new DefaultHttpRequestRetryHandler(0, false)) // 禁用重试
                    .build();

        } catch (Exception e) {
            logger.error("创建HTTP客户端时出错", e);
            throw new RuntimeException(e);
        }
    }

    public static CloseableHttpClient createHttpClientNew(String host, int port, String user, String pass) {

        try {
            // 1. 代理
            HttpHost proxy = new HttpHost(host, port);

            // 2. SSL：自定义指纹 / 协议
            SSLContext sslContext = SSLContexts.custom()
                    // 生产别全信
                    .loadTrustMaterial(null, (c, a) -> true)
                    .build();

            SSLConnectionSocketFactory sslFactory =
                    new SSLConnectionSocketFactory(
                            sslContext,
                            getProtocolsForFingerprint(),
                            getCipherSuitesForFingerprint(),
                            // DEV 可用 Noop，PROD 换成 Default
                            new DefaultHostnameVerifier()
                    );

            // 3. 连接池
            Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.getSocketFactory())
                    .register("https", sslFactory)
                    .build();

            PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager(registry);
            cm.setMaxTotal(100);
            cm.setDefaultMaxPerRoute(20);

            // 4. 代理凭据
            CredentialsProvider cp = new BasicCredentialsProvider();
            cp.setCredentials(new AuthScope(host, port),
                    new UsernamePasswordCredentials(user, pass));

            // 5. 请求级别配置
            RequestConfig rc = RequestConfig.custom()
                    .setProxy(proxy)
                    .setConnectTimeout(10_000)
                    .setSocketTimeout(15_000)
                    .setConnectionRequestTimeout(5_000)
                    .build();

            // 6. 构建 Client
            return HttpClients.custom()
                    .setConnectionManager(cm)
                    .setDefaultCredentialsProvider(cp)
                    .setDefaultRequestConfig(rc)
                    .setRetryHandler(new DefaultHttpRequestRetryHandler(0, false))
                    .build();

        } catch (Exception e) {
            throw new IllegalStateException("Init HttpClient failed", e);
        }
    }


    /**
     * 根据指纹获取User-Agent
     *
     * @return User-Agent字符串
     */
    private String getUserAgent() {
        switch (fingerprint.toLowerCase()) {
            case "chrome_106":
                return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
            case "firefox_105":
                return "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:105.0) Gecko/20100101 Firefox/105.0";
            case "safari_16":
                return "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15";
            default:
                return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
        }
    }

    /**
     * 根据指纹获取加密套件
     *
     * @return 加密套件数组
     */
    private static String[] getCipherSuitesForFingerprint() {
        switch (fingerprint.toLowerCase()) {
            case "chrome_106":
                return new String[]{
                        "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256",
                        "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
                        "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384",
                        "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
                };
            case "firefox_105":
                return new String[]{
                        "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256",
                        "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
                        "TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256",
                        "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256",
                        "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384",
                        "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
                        "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA",
                        "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA",
                        "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA",
                        "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA",
                        "TLS_RSA_WITH_AES_128_GCM_SHA256",
                        "TLS_RSA_WITH_AES_256_GCM_SHA384",
                        "TLS_RSA_WITH_AES_128_CBC_SHA",
                        "TLS_RSA_WITH_AES_256_CBC_SHA"
                };
            case "safari_16":
                return new String[]{
                        "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384",
                        "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
                        "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256",
                        "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
                        "TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256",
                        "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256",
                        "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384",
                        "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384",
                        "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256",
                        "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256",
                        "TLS_RSA_WITH_AES_256_GCM_SHA384",
                        "TLS_RSA_WITH_AES_128_GCM_SHA256",
                        "TLS_RSA_WITH_AES_256_CBC_SHA256",
                        "TLS_RSA_WITH_AES_128_CBC_SHA256"
                };
            default:
                return null;
        }
    }

    /**
     * 根据指纹获取支持的TLS协议版本
     *
     * @return TLS协议版本数组
     */
    public static String[] getProtocolsForFingerprint() {
        switch (fingerprint.toLowerCase()) {
            case "chrome_106":
            case "firefox_105":
            case "safari_16":
                return new String[]{"TLSv1.2", "TLSv1.3"};
            default:
                return new String[]{"TLSv1.2", "TLSv1.3"};
        }
    }

    /**
     * 关闭客户端
     */
    public void close() {
        try {
            httpClient.close();
            logger.info("HTTP客户端已关闭");
        } catch (IOException e) {
            logger.error("关闭HTTP客户端时出错", e);
        }
    }

    /**
     * 自定义SSL连接套接字工厂，用于设置TLS指纹
     */
    public static class CustomSSLConnectionSocketFactory extends SSLConnectionSocketFactory {
        private final String[] protocols;
        private final String[] cipherSuites;

        public CustomSSLConnectionSocketFactory(SSLContext sslContext, String[] protocols, String[] cipherSuites) {
            super(sslContext);
            this.protocols = protocols;
            this.cipherSuites = cipherSuites;
        }

        @Override
        protected void prepareSocket(SSLSocket socket) {
            if (protocols != null) {
                socket.setEnabledProtocols(protocols);
            }
            if (cipherSuites != null) {
                socket.setEnabledCipherSuites(cipherSuites);
            }
        }
    }
}
