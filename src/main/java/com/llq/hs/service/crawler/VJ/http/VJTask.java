package com.llq.hs.service.crawler.VJ.http;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.base.Strings;
import com.llq.hs.service.crawler.VJ.dto.VjResultDataDto;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.Credentials;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.params.ConnRoutePNames;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

import static com.llq.hs.service.crawler.VJ.http.VjHttpUtil.readHtmlContentFromEntity;


/**
 * VJ航司爬虫
 */
@Component
public class VJTask {
    private static Logger logger = LoggerFactory.getLogger(VJTask.class);
    // private static final LinkedBlockingQueue<String> ipList = new LinkedBlockingQueue<String>();


    private static final List<String> USER_AGENTS = Arrays.asList(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_4_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/124.0.2470.97 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64; rv:124.0) Gecko/20100101 Firefox/124.0",
            "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 OPR/90.0.4420.60",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Mobile/15E148 Safari/604.1"
    );

    public static void main(String[] args) {
        for (int i = 0; i < 2; i++) {

        String requestInfo = "{\n" +
                "    \"air_type\": \"VJ\",\n" +
                "    \"data\": {\n" +
                "        \"searchParams\": {\n" +
                "            \"currency\": \"USD\",\n" +
                "            \"directOnly\": false,\n" +
                "            \"ownAirlineOnly\": false,\n" +
                "            \"passengersAmount\": {\n" +
                "                \"adults\": 1,\n" +
                "                \"children\": 0,\n" +
                "                \"infants\": 0\n" +
                "            },\n" +
                "            \"promoCode\": \"\",\n" +
                "            \"redemption\": false,\n" +
                "            \"routes\": [\n" +
                "                {\n" +
                "                    \"departureDate\": \"2025-11-11\",\n" +
                "                    \"destination\": \"HKT\",\n" +
                "                    \"origin\": \"BKK\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"searchType\": \"EXACT\",\n" +
                "            \"subsidizedPassengerTypes\": [],\n" +
                "            \"tripType\": \"ONE_WAY\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"token\": \"\"\n" +
                "}";

        System.out.println("开始执行VJ数据采集...");

        try {
            VJTask vjTask = new VJTask();
            System.out.println("开始获取代理IP和发送请求...");
            String s = vjTask.wrapperRequest(requestInfo);

            if (s.startsWith("ERROR_")) {
                System.err.println("请求失败: " + s);
                return;
            }

            System.out.println("开始解析返回数据...");
            List<VjResultDataDto> vjResultDataDto = new VJDataInfo().dataInfo(s,"");
            System.out.println("解析完成，结果:");
            System.out.println(vjResultDataDto);
        } catch (Exception e) {
            System.err.println("执行过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }

        }
    }

    /**
     * 采集方法入口
     *
     * @param requestInfo
     * @return
     */
    public String wrapperRequest(String requestInfo) {

        JSONObject jsonObject = JSONObject.parseObject(requestInfo);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONObject searchParams = data.getJSONObject("searchParams");
        JSONObject passengersAmount = searchParams.getJSONObject("passengersAmount");
        String cur = searchParams.getString("currency");

        int adults = passengersAmount.getIntValue("adults");
        int children = passengersAmount.getIntValue("children");
        int infants = passengersAmount.getIntValue("infants");

        JSONObject route = searchParams.getJSONArray("routes").getJSONObject(0);
        String departureDate = route.getString("departureDate");
        String destination = route.getString("destination");
        String origin = route.getString("origin");


        //DefaultHttpClient client = VjHttpUtil.getHttpClient("");

        /*if (ipList.isEmpty()) {
            String getProxy = getProxyIP(client);
            String[] split = getProxy.split("\r\n");
            for (String s : split) {
                ipList.offer(s);
            }
        }*/

        String poll = QueueUtil.poll();
        if (Strings.isNullOrEmpty(poll)){
           return "ERROR_ip_get_null";
        }
        String s1 = poll.split(",")[0];
        String[] split1 = s1.split(":");
        //String[] split = getProxy.split(":");

        String proxy_host = split1[0];
        int proxy_port = Integer.parseInt(split1[1].replaceAll("\r", "").replaceAll("\n", ""));
        String proxy_user = "luluqi";
        String proxy_pass = "luluqi";



//        String proxy_host = "sg.haiwai-ip.com";
//        int proxy_port = 1464;
//        String proxy_user = "user-sub.HGOODAIR3579-country-ru-type-dc-session-" + RandomUtil.randomString(6);
//        String proxy_pass = "GOODAIR1589";

//        String proxy_host = "pr.roxlabs.cn";
//        int proxy_port = 4600;
//        String proxy_user = "user-ggb1234-region-au-sessid-"+RandomUtil.randomString(6)+"-sesstime-5-keep-true";
//        String proxy_pass = "aaa1234567";

        String proxy_str = proxy_host + ":" + proxy_port + "@" + proxy_user + ":" + proxy_pass;
        logger.info("本次请求代理:" + proxy_str);

        DefaultHttpClient client = VjHttpUtil.getHttpClient("");
//        CloseableHttpClient client = createHttpClientNew(proxy_host, proxy_post, proxy_user, proxy_pass);
        proxy_ip(client, proxy_host, proxy_port, proxy_user, proxy_pass);
//         CloseableHttpClient client = createHttpClientNew(proxy_host, proxy_post, proxy_user, proxy_pass);

        String search = search(client, origin, destination, departureDate, adults, children, cur);

//        String search = searchNew(client, origin, destination, departureDate, adults, children, cur, proxy_user, proxy_pass, proxy_host, proxy_port);
        if (search.contains("ERROR_")) {
            return search;
        }

        //String searchInfo = searchInfo(search);
        return search;
    }

    private String searchNew(DefaultHttpClient client, String origin, String destination, String departureDate, int adults, int children, String cur, String proxy_user, String proxy_pass, String proxy_host, int proxy_port) {

        String param = "{\n" +
                "    \"air_type\": \"VJ\",\n" +
                "    \"token\": \"LYL240923\",\n" +
                "    \"data\": {\n" +
                "        \"pool\": 0,\n" +
                "        \"proxy_config\": {\n" +
                "            \"proxyUser\": \"" + proxy_user + "\",\n" +
                "            \"proxyPass\": \"" + proxy_pass + "\",\n" +
                "            \"proxyHost\": \"" + proxy_host + "\",\n" +
                "            \"proxyPort\": "+proxy_port+"\n" +
                "        },\n" +
                "        \"searchParams\": {\n" +
                "            \"currency\": \"" + cur + "\",\n" +
                "            \"departureDate\": \"" + departureDate + "\",\n" +
                "            \"daysBeforeDeparture\": 0,\n" +
                "            \"daysAfterDeparture\": 0,\n" +
                "            \"departurePlace\": \"" + origin + "\",\n" +
                "            \"arrival\": \"" + destination + "\",\n" +
                "            \"oneway\": 1,\n" +
                "            \"adultCount\": " + adults + children + ",\n" +
                "            \"childCount\": 0,\n" +
                "            \"infantCount\": 0\n" +
                "        }\n" +
                "    }\n" +
                "}";

        String params = "{\"air_type\":\"VJ\",\"token\":\"LYL240923\",\"data\":{\"pool\":6165,\"proxy_config\":{\"proxyUser\":\""+proxy_user+"\",\"proxyPass\":\""+proxy_pass+"\",\"proxyHost\":\""+proxy_host+"\",\"proxyPort\":\""+proxy_port+"\"},\"searchParams\":{\"currency\":\"" + cur + "\",\"departureDate\":\"" + departureDate + "\",\"daysBeforeDeparture\":0,\"daysAfterDeparture\":0,\"departurePlace\":\"" + origin + "\",\"arrival\":\"" + destination + "\",\"oneway\":1,\"adultCount\":" + (adults + children) + ",\"childCount\":0,\"infantCount\":0}}}";
//        String params = "{\"air_type\":\"VJ\",\"token\":\"LYL240923\",\"data\":{\"pool\":6165,\"proxy_config\":{\"proxyUser\":\"user-ggb1234-region-au-sessid-auE1yA2Btt-sesstime-5-keep-true\",\"proxyPass\":\"aaa1234567\",\"proxyHost\":\"pr.roxlabs.cn\",\"proxyPort\":\"4600\"},\"searchParams\":{\"currency\":\"AUD\",\"departureDate\":\"2025-10-12\",\"daysBeforeDeparture\":0,\"daysAfterDeparture\":0,\"departurePlace\":\"HAN\",\"arrival\":\"BMV\",\"oneway\":1,\"adultCount\":1,\"childCount\":0,\"infantCount\":0}}}";
        String url = "http://************:8000/search";
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-Type", "application/json");
        try {
            httpPost.setEntity(new StringEntity(params, "UTF-8"));
            HttpResponse httpResponse = client.execute(httpPost);
//            int code = httpResponse.getStatusLine().getStatusCode();
            String content = readHtmlContentFromEntity(httpResponse.getEntity());
//            String codeJson = JSONObject.parseObject(content).getString("code");
//            if (!codeJson.equals("200")) {
//                logger.info("python抓取,statusCode:" + codeJson + ",content" + content);
//            } else {
//                logger.info("python抓取,statusCode:" + codeJson);
//            }
//            if (!code.equals("200")) {
//            if (code != 200) {
//                return "ERROR_首页失败";
//            }
            return content;
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("{}>>{}请求Response>>python抓取异常");
            return "ERROR_python抓取";
        }

    }


    /**
     * 采集方法
     *
     * @param client
     * @param fromCity
     * @param toCity
     * @param fromDate
     * @param adult
     * @param child
     * @param cur
     * @return
     */
    public String search(CloseableHttpClient client, String fromCity, String toCity, String fromDate, int adult, int child, String cur) {
        String[] split = fromDate.split("-");
        String url = "https://th.vietjetair.com/flight/getFlights?tripType=onewaytrip&from_where=" + fromCity + "&to_where=" + toCity + "&start=" + split[2] + "/" + split[1] + "/" + split[0] + "&end=" + split[2] + "/" + split[1] + "/" + split[0] + "&adultCount=" + (adult + child) + "&childCount=0&infantCount=0&promoCode=&currency=" + cur;

        HttpPatch httpPatch = new HttpPatch("https://vietjet-api.vietjetair.com/booking/api/v1/search-flight");
        httpPatch.addHeader("accept", "application/json");
        httpPatch.addHeader("accept-language", "zh-cn");
        httpPatch.addHeader("cache-control", "no-cache");
        httpPatch.addHeader("content-language", "zh-cn");
        httpPatch.addHeader("content-type", "application/json");
        httpPatch.addHeader("origin", "https://www.vietjetair.com");
        httpPatch.addHeader("pragma", "no-cache");
        httpPatch.addHeader("priority", "u=1, i");
        httpPatch.addHeader("referer", "https://www.vietjetair.com/");
        httpPatch.addHeader("sec-ch-ua", "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\"");
        httpPatch.addHeader("sec-ch-ua-mobile", "?0");
        httpPatch.addHeader("sec-ch-ua-platform", "\"Windows\"");
        httpPatch.addHeader("sec-fetch-dest", "empty");
        httpPatch.addHeader("sec-fetch-mode", "cors");
        httpPatch.addHeader("sec-fetch-site", "same-site");
        httpPatch.addHeader("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36");

        Random random = new Random();
        StringBuilder requestIdBuilder = new StringBuilder();
        for (int i = 0; i < 12; i++) {
            int randomChar = random.nextInt(36); // 0-9, A-Z
            if (randomChar < 10) {
                requestIdBuilder.append((char) ('0' + randomChar));
            } else {
                requestIdBuilder.append((char) ('A' + randomChar - 10));
            }
        }
        long currentTimeMillis = System.currentTimeMillis();
        String user_agent_ls_data = UUID.randomUUID().toString().toLowerCase();
        String x_power_web_s_d = UUID.randomUUID().toString().toLowerCase();
        String requestId = requestIdBuilder.toString() + "-" + currentTimeMillis;

        String params = "{\n" +
                "  \"adultCount\": \"" + adult + "\",\n" +
                "  \"arrival\": \"" + toCity + "\",\n" +
                "  \"childCount\": \"" + child + "\",\n" +
                "  \"currency\": \"" + "USD" + "\",\n" +
                "  \"daysAfterDeparture\": \"0\",\n" +
                "  \"daysBeforeDeparture\": \"0\",\n" +
                "  \"departureDate\": \"" + fromDate + "\",\n" +
                "  \"departurePlace\": \"" + fromCity + "\",\n" +
                "  \"infantCount\": \"0\",\n" +
                "  \"oneway\": \"1\",\n" +
                "  \"requestId\": \"" + requestId + "\",\n" +
                "  \"sessionId\": \"\",\n" +
                "  \"user-agent-ls-data\": \"" + user_agent_ls_data + "\",\n" +
                "  \"x-power-web-s-d\": \"" + x_power_web_s_d + "\"\n" +
                "}";


//            String s = kaige_sha(params);
        String s = "adultCount="+adult+"&arrival="+toCity+"&childCount="+child+"&currency="+cur+"&daysAfterDeparture=0&daysBeforeDeparture=0&departureDate="+fromDate+"&departurePlace="+fromCity+"&infantCount=0&oneway=1&requestId="+requestId+"&sessionId=&user-agent-ls-data="+user_agent_ls_data+"&x-power-web-s-d="+x_power_web_s_d;

        try {

            String signature = sha256UrlEncode(s);


            // 重新构造 params，并添加 signature
            params = params.substring(0, params.length() - 2) + ",\n" + // 删除最后的 "}\n"
                    "  \"_signature\": \"" + signature + "\"\n" + // 添加 signature
                    "}"; // 重新添加结束符号
            String kpsKey = getKpsKey(params);

            JSONObject jsonData = new JSONObject();
            jsonData.put("encrypted", kpsKey);

            StringEntity entity = new StringEntity(jsonData.toString(), "UTF-8");
            httpPatch.setEntity(entity);
            HttpResponse execute = client.execute(httpPatch);
            logger.debug("{}>>{}请求Response>>{}" + "1" + "search" + execute.getStatusLine().toString());
            int code = execute.getStatusLine().getStatusCode();
            String content = readHtmlContentFromEntity(execute.getEntity());
            logger.debug("search响应:" + code + ";响应:" + content);
//            if (code.equals("200")) {
            if (code == 200) {
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("code", code);
                jsonObject1.put("data", content);
                return jsonObject1.toJSONString();
            } else {
                return "ERROR_search失败";
            }
//            ////System.out.println(code + content);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("{}>>{}请求Response>>search异常");
            return "ERROR_search异常";
        }
    }


    public String searchFlight(DefaultHttpClient httpClient, String fromCity, String toCity, String fromDate, int adult, int child, String cur) {
        HttpPatch httpPatch = new HttpPatch("https://vietjet-api.vietjetair.com/booking/api/v1/search-flight");
        httpPatch.addHeader("accept", "application/json");
        httpPatch.addHeader("accept-language", "zh-cn");
        httpPatch.addHeader("cache-control", "no-cache");
        httpPatch.addHeader("content-language", "zh-cn");
        httpPatch.addHeader("content-type", "application/json");
        httpPatch.addHeader("origin", "https://www.vietjetair.com");
        httpPatch.addHeader("pragma", "no-cache");
        httpPatch.addHeader("priority", "u=1, i");
        httpPatch.addHeader("referer", "https://www.vietjetair.com/");
        httpPatch.addHeader("sec-ch-ua", "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\"");
        httpPatch.addHeader("sec-ch-ua-mobile", "?0");
        httpPatch.addHeader("sec-ch-ua-platform", "\"Windows\"");
        httpPatch.addHeader("sec-fetch-dest", "empty");
        httpPatch.addHeader("sec-fetch-mode", "cors");
        httpPatch.addHeader("sec-fetch-site", "same-site");
        httpPatch.setHeader("accept-encoding", "gzip, deflate, sdch");
        httpPatch.addHeader("user-agent", getRandomUserAgent());
        try {
            Random random = new Random();
            // 生成requestId
            StringBuilder requestIdBuilder = new StringBuilder();

            for (int i = 0; i < 12; i++) {
                int randomChar = random.nextInt(36); // 0-9, A-Z
                if (randomChar < 10) {
                    requestIdBuilder.append((char) ('0' + randomChar));
                } else {
                    requestIdBuilder.append((char) ('A' + randomChar - 10));
                }
            }
            long currentTimeMillis = System.currentTimeMillis();

            String params = "{\n" +
                    "  \"adultCount\": \"" + (adult + child) + "\",\n" +
                    "  \"arrival\": \"" + toCity + "\",\n" +
                    "  \"childCount\": \"0\",\n" +
                    "  \"currency\": \"" + cur + "\",\n" +
                    "  \"daysAfterDeparture\": \"0\",\n" +
                    "  \"daysBeforeDeparture\": \"0\",\n" +
                    "  \"departureDate\": \"" + fromDate + "\",\n" +
                    "  \"departurePlace\": \"" + fromCity + "\",\n" +
                    "  \"infantCount\": \"0\",\n" +
                    "  \"oneway\": \"1\",\n" +
                    "  \"requestId\": \"" + requestIdBuilder.toString() + "-" + currentTimeMillis + "\",\n" +
                    "  \"sessionId\": \"\",\n" +
                    "  \"user-agent-ls-data\": \"" + UUID.randomUUID().toString().toLowerCase() + "\",\n" +
                    "  \"x-power-web-s-d\": \"" + UUID.randomUUID().toString().toLowerCase() + "\"\n" +
                    "}";

            String user_agent_ls_data = UUID.randomUUID().toString().toLowerCase();
            String x_power_web_s_d = UUID.randomUUID().toString().toLowerCase();
            String requestId = requestIdBuilder.toString() + "-" + currentTimeMillis;
//            String s = kaige_sha(params);

            String s = "adultCount="+adult+"&arrival="+toCity+"&childCount="+child+"&currency="+cur+"&daysAfterDeparture=0&daysBeforeDeparture=0&departureDate="+fromDate+"&departurePlace="+fromCity+"&infantCount=0&oneway=1&requestId="+requestId+"&sessionId=&user-agent-ls-data="+user_agent_ls_data+"&x-power-web-s-d="+x_power_web_s_d;
            String signature = sha256UrlEncode(s);

            // 重新构造 params，并添加 signature
            params = params.substring(0, params.length() - 2) + ",\n" + // 删除最后的 "}\n"
                    "  \"_signature\": \"" + signature + "\"\n" + // 添加 signature
                    "}"; // 重新添加结束符号
            String kpsKey = getKpsKey(params);
            if (StringUtils.isBlank(kpsKey)) {
                return "ERROR_搜索航班失败_用徐哥接口对数据加密是吧";
            }

            JSONObject jsonData = new JSONObject();
            jsonData.put("encrypted", kpsKey);
            StringEntity entity = new StringEntity(jsonData.toString(), "UTF-8");
            httpPatch.setEntity(entity);
            HttpResponse response = httpClient.execute(httpPatch);
            int statusCode = response.getStatusLine().getStatusCode();

            String info = "";
            if (statusCode != 200) {
                info = readHtmlContentFromEntity(response.getEntity());
                logger.info("VJWEBcode" + statusCode);
                return info;
            }
            info = readHtmlContentFromEntity(response.getEntity());
            logger.info("VJWEBcode" + statusCode);
            return info;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "ERROR_搜索航班失败_请重试";
    }


    public static String getRandomUserAgent() {
        int randomIndex = ThreadLocalRandom.current().nextInt(USER_AGENTS.size());
        return USER_AGENTS.get(randomIndex);
    }

    public String getKpsKey(String data) {
        try {
            URL url = new URL("http://47.52.11.194:4080/vj_enc");
            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setRequestMethod("POST");

            // 设置请求 headers
            httpConn.setRequestProperty("Accept", "application/json"); // 你可以根据需要调整
            httpConn.setRequestProperty("Content-Type", "application/json; utf-8");
            httpConn.setRequestProperty("User-Agent", "Mozilla/5.0");

            httpConn.setDoOutput(true); // 允许输出
            httpConn.setDoInput(true); // 允许输入

            // 写入请求体
            try (OutputStream os = httpConn.getOutputStream()) {
                byte[] input = data.getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            // 获取响应
            InputStream responseStream = httpConn.getResponseCode() / 100 == 2
                    ? httpConn.getInputStream()
                    : httpConn.getErrorStream();
            Scanner s = new Scanner(responseStream).useDelimiter("\\A");
            String response = s.hasNext() ? s.next() : "";
            return response;

        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public String kaige_sha(String data) {
        String url = "http://172.105.200.59:8290/qsjson";
        String jsonInputString = data;

        try {
            // 创建 URL 对象
            URL obj = new URL(url);
            // 打开连接
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();
            // 设置请求方法
            con.setRequestMethod("POST");
            // 设置请求头
            con.setRequestProperty("User-Agent", "Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html) Chrome/W.X.Y.Z Safari/537.36");
            con.setRequestProperty("Content-Type", "application/json");
            // 启用输入输出流
            con.setDoOutput(true);

            // 发送请求
            try (OutputStream os = con.getOutputStream()) {
                byte[] input = jsonInputString.getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            // 获取响应代码
            int responseCode = con.getResponseCode();
            // 获取响应
            InputStream responseStream = con.getResponseCode() / 100 == 2
                    ? con.getInputStream()
                    : con.getErrorStream();
            Scanner s = new Scanner(responseStream).useDelimiter("\\A");
            String response = s.hasNext() ? s.next() : "";
            // 关闭连接
            con.disconnect();
            return response;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String sha256UrlEncode(String data) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        // URL 编码
        String encodedData = data;

        // 创建 SHA-256 散列
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = digest.digest(encodedData.getBytes("UTF-8"));

        // 转换为十六进制字符串
        StringBuilder hexString = new StringBuilder();
        for (byte b : hashBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }

        return hexString.toString();
    }


    public void proxy_ip(DefaultHttpClient client, String proxy_host, int proxy_post, String proxy_user, String proxy_pass) {

//        String proxyHost = "t101.juliangip.cc";
//        Integer proxyPort = 11890;
//        String proxyUser = "18201068924";
//        String proxyPass = "hhXwlqfn";

        String proxyHost = proxy_host;
        Integer proxyPort = proxy_post;
        String proxyUser = proxy_user;
        String proxyPass = proxy_pass;

//        String proxy_url = "http://" + proxyUser + ":" + proxyPass + "@" + proxyHost + ":" + proxyPort;
//        logger.debug("{}本次client所用代理:" + proxy_url);

        HttpHost proxy = new HttpHost(proxyHost, proxyPort, "http");
        client.getParams().setParameter(ConnRoutePNames.DEFAULT_PROXY, proxy);
        AuthScope auth = new AuthScope(proxyHost, proxyPort);
        Credentials credentials = new org.apache.http.auth.NTCredentials(proxyUser, proxyPass, "", "");
        client.getCredentialsProvider().setCredentials(auth, credentials);
    }

}
