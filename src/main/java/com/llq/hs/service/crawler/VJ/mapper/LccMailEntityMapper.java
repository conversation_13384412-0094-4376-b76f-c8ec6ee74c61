package com.llq.hs.service.crawler.VJ.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.llq.hs.service.crawler.VJ.entity.LccMailEntity;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【lcc_mail】的数据库操作Mapper
* @createDate 2025-07-12 14:42:03
* @Entity com.payask.saas667.lcc.mail.LccMailEntity
*/
public interface LccMailEntityMapper extends BaseMapper<LccMailEntity> {

    /**
     *
     * @return
     * @param code
     */
    Date selectMaxReceiveDate(String code);
}




